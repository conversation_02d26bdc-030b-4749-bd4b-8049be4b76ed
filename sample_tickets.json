[{"id": 1, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 1: Issue with Service 2", "date": "2025-01-02T10:00:00", "date_modified": "2025-01-02T11:00:00", "open_date": "2025-01-02T10:00:00", "close_date": "2025-01-03T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 1. This is a test case.", "urgency": 2, "impact": 2, "priority": 2, "is_deleted": 0, "additional_field": {"id": 1, "vem_num": "VEM-1", "aevm_num": "AEVM-1", "atg_num": "ATG-1", "start_time": "2025-01-02 10:00:00", "end_time": "2025-01-02 12:00:00"}, "ticket_users": [{"id": 1, "user": {"id": 102, "name": "user.102", "first_name": "First102", "last_name": "Last102"}, "type": 1, "ticket_id": 1}, {"id": 2, "user": {"id": 202, "name": "tech.202", "first_name": "TechFirst202", "last_name": "TechLast202"}, "type": 2, "ticket_id": 1}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 2, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 2: Issue with Service 3", "date": "2025-01-03T10:00:00", "date_modified": "2025-01-03T11:00:00", "open_date": "2025-01-03T10:00:00", "close_date": "2025-01-04T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 2. This is a test case.", "urgency": 3, "impact": 3, "priority": 3, "is_deleted": 0, "additional_field": {"id": 2, "vem_num": "VEM-2", "aevm_num": "AEVM-2", "atg_num": "ATG-2", "start_time": "2025-01-03 10:00:00", "end_time": "2025-01-03 12:00:00"}, "ticket_users": [{"id": 3, "user": {"id": 103, "name": "user.103", "first_name": "First103", "last_name": "Last103"}, "type": 1, "ticket_id": 2}, {"id": 4, "user": {"id": 203, "name": "tech.203", "first_name": "TechFirst203", "last_name": "TechLast203"}, "type": 2, "ticket_id": 2}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 3, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 3: Issue with Service 4", "date": "2025-01-04T10:00:00", "date_modified": "2025-01-04T11:00:00", "open_date": "2025-01-04T10:00:00", "close_date": "2025-01-05T10:00:00", "solve_date": "2025-01-11T10:00:00", "status": 6, "content": "Detailed description for sample ticket 3. This is a test case.", "urgency": 4, "impact": 1, "priority": 4, "is_deleted": 0, "additional_field": {"id": 3, "vem_num": "VEM-3", "aevm_num": "AEVM-3", "atg_num": "ATG-3", "start_time": "2025-01-04 10:00:00", "end_time": "2025-01-04 12:00:00"}, "ticket_users": [{"id": 5, "user": {"id": 104, "name": "user.104", "first_name": "First104", "last_name": "Last104"}, "type": 1, "ticket_id": 3}, {"id": 6, "user": {"id": 204, "name": "tech.204", "first_name": "TechFirst204", "last_name": "TechLast204"}, "type": 2, "ticket_id": 3}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": 168.0}, {"id": 4, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 4: Issue with Service 5", "date": "2025-01-05T10:00:00", "date_modified": "2025-01-05T11:00:00", "open_date": "2025-01-05T10:00:00", "close_date": "2025-01-06T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 4. This is a test case.", "urgency": 1, "impact": 2, "priority": 5, "is_deleted": 0, "additional_field": {"id": 4, "vem_num": "VEM-4", "aevm_num": "AEVM-4", "atg_num": "ATG-4", "start_time": "2025-01-05 10:00:00", "end_time": "2025-01-05 12:00:00"}, "ticket_users": [{"id": 7, "user": {"id": 105, "name": "user.105", "first_name": "First105", "last_name": "Last105"}, "type": 1, "ticket_id": 4}, {"id": 8, "user": {"id": 205, "name": "tech.205", "first_name": "TechFirst205", "last_name": "TechLast205"}, "type": 2, "ticket_id": 4}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 5, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 5: Issue with Service 1", "date": "2025-01-06T10:00:00", "date_modified": "2025-01-06T11:00:00", "open_date": "2025-01-06T10:00:00", "close_date": "2025-01-07T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 5. This is a test case.", "urgency": 2, "impact": 3, "priority": 1, "is_deleted": 0, "additional_field": {"id": 5, "vem_num": "VEM-5", "aevm_num": "AEVM-5", "atg_num": "ATG-5", "start_time": "2025-01-06 10:00:00", "end_time": "2025-01-06 12:00:00"}, "ticket_users": [{"id": 9, "user": {"id": 101, "name": "user.101", "first_name": "First101", "last_name": "Last101"}, "type": 1, "ticket_id": 5}, {"id": 10, "user": {"id": 201, "name": "tech.201", "first_name": "TechFirst201", "last_name": "TechLast201"}, "type": 2, "ticket_id": 5}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 6, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 6: Issue with Service 2", "date": "2025-01-07T10:00:00", "date_modified": "2025-01-07T11:00:00", "open_date": "2025-01-07T10:00:00", "close_date": "2025-01-08T10:00:00", "solve_date": "2025-01-14T10:00:00", "status": 6, "content": "Detailed description for sample ticket 6. This is a test case.", "urgency": 3, "impact": 1, "priority": 2, "is_deleted": 0, "additional_field": {"id": 6, "vem_num": "VEM-6", "aevm_num": "AEVM-6", "atg_num": "ATG-6", "start_time": "2025-01-07 10:00:00", "end_time": "2025-01-07 12:00:00"}, "ticket_users": [{"id": 11, "user": {"id": 102, "name": "user.102", "first_name": "First102", "last_name": "Last102"}, "type": 1, "ticket_id": 6}, {"id": 12, "user": {"id": 202, "name": "tech.202", "first_name": "TechFirst202", "last_name": "TechLast202"}, "type": 2, "ticket_id": 6}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": 168.0}, {"id": 7, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 7: Issue with Service 3", "date": "2025-01-08T10:00:00", "date_modified": "2025-01-08T11:00:00", "open_date": "2025-01-08T10:00:00", "close_date": "2025-01-09T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 7. This is a test case.", "urgency": 4, "impact": 2, "priority": 3, "is_deleted": 0, "additional_field": {"id": 7, "vem_num": "VEM-7", "aevm_num": "AEVM-7", "atg_num": "ATG-7", "start_time": "2025-01-08 10:00:00", "end_time": "2025-01-08 12:00:00"}, "ticket_users": [{"id": 13, "user": {"id": 103, "name": "user.103", "first_name": "First103", "last_name": "Last103"}, "type": 1, "ticket_id": 7}, {"id": 14, "user": {"id": 203, "name": "tech.203", "first_name": "TechFirst203", "last_name": "TechLast203"}, "type": 2, "ticket_id": 7}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 8, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 8: Issue with Service 4", "date": "2025-01-09T10:00:00", "date_modified": "2025-01-09T11:00:00", "open_date": "2025-01-09T10:00:00", "close_date": "2025-01-10T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 8. This is a test case.", "urgency": 1, "impact": 3, "priority": 4, "is_deleted": 0, "additional_field": {"id": 8, "vem_num": "VEM-8", "aevm_num": "AEVM-8", "atg_num": "ATG-8", "start_time": "2025-01-09 10:00:00", "end_time": "2025-01-09 12:00:00"}, "ticket_users": [{"id": 15, "user": {"id": 104, "name": "user.104", "first_name": "First104", "last_name": "Last104"}, "type": 1, "ticket_id": 8}, {"id": 16, "user": {"id": 204, "name": "tech.204", "first_name": "TechFirst204", "last_name": "TechLast204"}, "type": 2, "ticket_id": 8}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 9, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 9: Issue with Service 5", "date": "2025-01-10T10:00:00", "date_modified": "2025-01-10T11:00:00", "open_date": "2025-01-10T10:00:00", "close_date": "2025-01-11T10:00:00", "solve_date": "2025-01-17T10:00:00", "status": 6, "content": "Detailed description for sample ticket 9. This is a test case.", "urgency": 2, "impact": 1, "priority": 5, "is_deleted": 0, "additional_field": {"id": 9, "vem_num": "VEM-9", "aevm_num": "AEVM-9", "atg_num": "ATG-9", "start_time": "2025-01-10 10:00:00", "end_time": "2025-01-10 12:00:00"}, "ticket_users": [{"id": 17, "user": {"id": 105, "name": "user.105", "first_name": "First105", "last_name": "Last105"}, "type": 1, "ticket_id": 9}, {"id": 18, "user": {"id": 205, "name": "tech.205", "first_name": "TechFirst205", "last_name": "TechLast205"}, "type": 2, "ticket_id": 9}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": 168.0}, {"id": 10, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 10: Issue with Service 1", "date": "2025-01-11T10:00:00", "date_modified": "2025-01-11T11:00:00", "open_date": "2025-01-11T10:00:00", "close_date": "2025-01-12T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 10. This is a test case.", "urgency": 3, "impact": 2, "priority": 1, "is_deleted": 0, "additional_field": {"id": 10, "vem_num": "VEM-10", "aevm_num": "AEVM-10", "atg_num": "ATG-10", "start_time": "2025-01-11 10:00:00", "end_time": "2025-01-11 12:00:00"}, "ticket_users": [{"id": 19, "user": {"id": 101, "name": "user.101", "first_name": "First101", "last_name": "Last101"}, "type": 1, "ticket_id": 10}, {"id": 20, "user": {"id": 201, "name": "tech.201", "first_name": "TechFirst201", "last_name": "TechLast201"}, "type": 2, "ticket_id": 10}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 11, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 11: Issue with Service 2", "date": "2025-01-12T10:00:00", "date_modified": "2025-01-12T11:00:00", "open_date": "2025-01-12T10:00:00", "close_date": "2025-01-13T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 11. This is a test case.", "urgency": 4, "impact": 3, "priority": 2, "is_deleted": 0, "additional_field": {"id": 11, "vem_num": "VEM-11", "aevm_num": "AEVM-11", "atg_num": "ATG-11", "start_time": "2025-01-12 10:00:00", "end_time": "2025-01-12 12:00:00"}, "ticket_users": [{"id": 21, "user": {"id": 102, "name": "user.102", "first_name": "First102", "last_name": "Last102"}, "type": 1, "ticket_id": 11}, {"id": 22, "user": {"id": 202, "name": "tech.202", "first_name": "TechFirst202", "last_name": "TechLast202"}, "type": 2, "ticket_id": 11}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 12, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 12: Issue with Service 3", "date": "2025-01-13T10:00:00", "date_modified": "2025-01-13T11:00:00", "open_date": "2025-01-13T10:00:00", "close_date": "2025-01-14T10:00:00", "solve_date": "2025-01-20T10:00:00", "status": 6, "content": "Detailed description for sample ticket 12. This is a test case.", "urgency": 1, "impact": 1, "priority": 3, "is_deleted": 0, "additional_field": {"id": 12, "vem_num": "VEM-12", "aevm_num": "AEVM-12", "atg_num": "ATG-12", "start_time": "2025-01-13 10:00:00", "end_time": "2025-01-13 12:00:00"}, "ticket_users": [{"id": 23, "user": {"id": 103, "name": "user.103", "first_name": "First103", "last_name": "Last103"}, "type": 1, "ticket_id": 12}, {"id": 24, "user": {"id": 203, "name": "tech.203", "first_name": "TechFirst203", "last_name": "TechLast203"}, "type": 2, "ticket_id": 12}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": 168.0}, {"id": 13, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 13: Issue with Service 4", "date": "2025-01-14T10:00:00", "date_modified": "2025-01-14T11:00:00", "open_date": "2025-01-14T10:00:00", "close_date": "2025-01-15T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 13. This is a test case.", "urgency": 2, "impact": 2, "priority": 4, "is_deleted": 0, "additional_field": {"id": 13, "vem_num": "VEM-13", "aevm_num": "AEVM-13", "atg_num": "ATG-13", "start_time": "2025-01-14 10:00:00", "end_time": "2025-01-14 12:00:00"}, "ticket_users": [{"id": 25, "user": {"id": 104, "name": "user.104", "first_name": "First104", "last_name": "Last104"}, "type": 1, "ticket_id": 13}, {"id": 26, "user": {"id": 204, "name": "tech.204", "first_name": "TechFirst204", "last_name": "TechLast204"}, "type": 2, "ticket_id": 13}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 14, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 14: Issue with Service 5", "date": "2025-01-15T10:00:00", "date_modified": "2025-01-15T11:00:00", "open_date": "2025-01-15T10:00:00", "close_date": "2025-01-16T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 14. This is a test case.", "urgency": 3, "impact": 3, "priority": 5, "is_deleted": 0, "additional_field": {"id": 14, "vem_num": "VEM-14", "aevm_num": "AEVM-14", "atg_num": "ATG-14", "start_time": "2025-01-15 10:00:00", "end_time": "2025-01-15 12:00:00"}, "ticket_users": [{"id": 27, "user": {"id": 105, "name": "user.105", "first_name": "First105", "last_name": "Last105"}, "type": 1, "ticket_id": 14}, {"id": 28, "user": {"id": 205, "name": "tech.205", "first_name": "TechFirst205", "last_name": "TechLast205"}, "type": 2, "ticket_id": 14}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 15, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 15: Issue with Service 1", "date": "2025-01-16T10:00:00", "date_modified": "2025-01-16T11:00:00", "open_date": "2025-01-16T10:00:00", "close_date": "2025-01-17T10:00:00", "solve_date": "2025-01-23T10:00:00", "status": 6, "content": "Detailed description for sample ticket 15. This is a test case.", "urgency": 4, "impact": 1, "priority": 1, "is_deleted": 0, "additional_field": {"id": 15, "vem_num": "VEM-15", "aevm_num": "AEVM-15", "atg_num": "ATG-15", "start_time": "2025-01-16 10:00:00", "end_time": "2025-01-16 12:00:00"}, "ticket_users": [{"id": 29, "user": {"id": 101, "name": "user.101", "first_name": "First101", "last_name": "Last101"}, "type": 1, "ticket_id": 15}, {"id": 30, "user": {"id": 201, "name": "tech.201", "first_name": "TechFirst201", "last_name": "TechLast201"}, "type": 2, "ticket_id": 15}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": 168.0}, {"id": 16, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 16: Issue with Service 2", "date": "2025-01-17T10:00:00", "date_modified": "2025-01-17T11:00:00", "open_date": "2025-01-17T10:00:00", "close_date": "2025-01-18T10:00:00", "solve_date": "2025-01-24T10:00:00", "status": 6, "content": "Detailed description for sample ticket 16. This is a test case.", "urgency": 1, "impact": 2, "priority": 2, "is_deleted": 0, "additional_field": {"id": 16, "vem_num": "VEM-16", "aevm_num": "AEVM-16", "atg_num": "ATG-16", "start_time": "2025-01-17 10:00:00", "end_time": "2025-01-17 12:00:00"}, "ticket_users": [{"id": 31, "user": {"id": 102, "name": "user.102", "first_name": "First102", "last_name": "Last102"}, "type": 1, "ticket_id": 16}, {"id": 32, "user": {"id": 202, "name": "tech.202", "first_name": "TechFirst202", "last_name": "TechLast202"}, "type": 2, "ticket_id": 16}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": 168.0}, {"id": 17, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 17: Issue with Service 3", "date": "2025-01-18T10:00:00", "date_modified": "2025-01-18T11:00:00", "open_date": "2025-01-18T10:00:00", "close_date": "2025-01-19T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 17. This is a test case.", "urgency": 2, "impact": 3, "priority": 3, "is_deleted": 0, "additional_field": {"id": 17, "vem_num": "VEM-17", "aevm_num": "AEVM-17", "atg_num": "ATG-17", "start_time": "2025-01-18 10:00:00", "end_time": "2025-01-18 12:00:00"}, "ticket_users": [{"id": 33, "user": {"id": 103, "name": "user.103", "first_name": "First103", "last_name": "Last103"}, "type": 1, "ticket_id": 17}, {"id": 34, "user": {"id": 203, "name": "tech.203", "first_name": "TechFirst203", "last_name": "TechLast203"}, "type": 2, "ticket_id": 17}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 18, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 18: Issue with Service 4", "date": "2025-01-19T10:00:00", "date_modified": "2025-01-19T11:00:00", "open_date": "2025-01-19T10:00:00", "close_date": "2025-01-20T10:00:00", "solve_date": "2025-01-26T10:00:00", "status": 6, "content": "Detailed description for sample ticket 18. This is a test case.", "urgency": 3, "impact": 1, "priority": 4, "is_deleted": 0, "additional_field": {"id": 18, "vem_num": "VEM-18", "aevm_num": "AEVM-18", "atg_num": "ATG-18", "start_time": "2025-01-19 10:00:00", "end_time": "2025-01-19 12:00:00"}, "ticket_users": [{"id": 35, "user": {"id": 104, "name": "user.104", "first_name": "First104", "last_name": "Last104"}, "type": 1, "ticket_id": 18}, {"id": 36, "user": {"id": 204, "name": "tech.204", "first_name": "TechFirst204", "last_name": "TechLast204"}, "type": 2, "ticket_id": 18}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": 168.0}, {"id": 19, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 19: Issue with Service 5", "date": "2025-01-20T10:00:00", "date_modified": "2025-01-20T11:00:00", "open_date": "2025-01-20T10:00:00", "close_date": "2025-01-21T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 19. This is a test case.", "urgency": 4, "impact": 2, "priority": 5, "is_deleted": 0, "additional_field": {"id": 19, "vem_num": "VEM-19", "aevm_num": "AEVM-19", "atg_num": "ATG-19", "start_time": "2025-01-20 10:00:00", "end_time": "2025-01-20 12:00:00"}, "ticket_users": [{"id": 37, "user": {"id": 105, "name": "user.105", "first_name": "First105", "last_name": "Last105"}, "type": 1, "ticket_id": 19}, {"id": 38, "user": {"id": 205, "name": "tech.205", "first_name": "TechFirst205", "last_name": "TechLast205"}, "type": 2, "ticket_id": 19}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 20, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 20: Issue with Service 1", "date": "2025-01-21T10:00:00", "date_modified": "2025-01-21T11:00:00", "open_date": "2025-01-21T10:00:00", "close_date": "2025-01-22T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 20. This is a test case.", "urgency": 1, "impact": 3, "priority": 1, "is_deleted": 0, "additional_field": {"id": 20, "vem_num": "VEM-20", "aevm_num": "AEVM-20", "atg_num": "ATG-20", "start_time": "2025-01-21 10:00:00", "end_time": "2025-01-21 12:00:00"}, "ticket_users": [{"id": 39, "user": {"id": 101, "name": "user.101", "first_name": "First101", "last_name": "Last101"}, "type": 1, "ticket_id": 20}, {"id": 40, "user": {"id": 201, "name": "tech.201", "first_name": "TechFirst201", "last_name": "TechLast201"}, "type": 2, "ticket_id": 20}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 21, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 21: Issue with Service 2", "date": "2025-01-22T10:00:00", "date_modified": "2025-01-22T11:00:00", "open_date": "2025-01-22T10:00:00", "close_date": "2025-01-23T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 21. This is a test case.", "urgency": 2, "impact": 3, "priority": 2, "is_deleted": 0, "additional_field": {"id": 21, "vem_num": "VEM-21", "aevm_num": "AEVM-21", "atg_num": "ATG-21", "start_time": "2025-01-22 10:00:00", "end_time": "2025-01-22 12:00:00"}, "ticket_users": [{"id": 41, "user": {"id": 102, "name": "user.102", "first_name": "First102", "last_name": "Last102"}, "type": 1, "ticket_id": 21}, {"id": 42, "user": {"id": 202, "name": "tech.202", "first_name": "TechFirst202", "last_name": "TechLast202"}, "type": 2, "ticket_id": 21}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 22, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 22: Issue with Service 3", "date": "2025-01-23T10:00:00", "date_modified": "2025-01-23T11:00:00", "open_date": "2025-01-23T10:00:00", "close_date": "2025-01-24T10:00:00", "solve_date": "2025-01-30T10:00:00", "status": 6, "content": "Detailed description for sample ticket 22. This is a test case.", "urgency": 3, "impact": 1, "priority": 3, "is_deleted": 0, "additional_field": {"id": 22, "vem_num": "VEM-22", "aevm_num": "AEVM-22", "atg_num": "ATG-22", "start_time": "2025-01-23 10:00:00", "end_time": "2025-01-23 12:00:00"}, "ticket_users": [{"id": 43, "user": {"id": 103, "name": "user.103", "first_name": "First103", "last_name": "Last103"}, "type": 1, "ticket_id": 22}, {"id": 44, "user": {"id": 203, "name": "tech.203", "first_name": "TechFirst203", "last_name": "TechLast203"}, "type": 2, "ticket_id": 22}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": 168.0}, {"id": 23, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 23: Issue with Service 4", "date": "2025-01-24T10:00:00", "date_modified": "2025-01-24T11:00:00", "open_date": "2025-01-24T10:00:00", "close_date": "2025-01-25T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 23. This is a test case.", "urgency": 4, "impact": 2, "priority": 4, "is_deleted": 0, "additional_field": {"id": 23, "vem_num": "VEM-23", "aevm_num": "AEVM-23", "atg_num": "ATG-23", "start_time": "2025-01-24 10:00:00", "end_time": "2025-01-24 12:00:00"}, "ticket_users": [{"id": 45, "user": {"id": 104, "name": "user.104", "first_name": "First104", "last_name": "Last104"}, "type": 1, "ticket_id": 23}, {"id": 46, "user": {"id": 204, "name": "tech.204", "first_name": "TechFirst204", "last_name": "TechLast204"}, "type": 2, "ticket_id": 23}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 24, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 24: Issue with Service 5", "date": "2025-01-25T10:00:00", "date_modified": "2025-01-25T11:00:00", "open_date": "2025-01-25T10:00:00", "close_date": "2025-01-26T10:00:00", "solve_date": "2025-01-31T10:00:00", "status": 6, "content": "Detailed description for sample ticket 24. This is a test case.", "urgency": 1, "impact": 1, "priority": 5, "is_deleted": 0, "additional_field": {"id": 24, "vem_num": "VEM-24", "aevm_num": "AEVM-24", "atg_num": "ATG-24", "start_time": "2025-01-25 10:00:00", "end_time": "2025-01-25 12:00:00"}, "ticket_users": [{"id": 47, "user": {"id": 105, "name": "user.105", "first_name": "First105", "last_name": "Last105"}, "type": 1, "ticket_id": 24}, {"id": 48, "user": {"id": 205, "name": "tech.205", "first_name": "TechFirst205", "last_name": "TechLast205"}, "type": 2, "ticket_id": 24}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": 168.0}, {"id": 25, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 25: Issue with Service 1", "date": "2025-01-26T10:00:00", "date_modified": "2025-01-26T11:00:00", "open_date": "2025-01-26T10:00:00", "close_date": "2025-01-27T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 25. This is a test case.", "urgency": 2, "impact": 2, "priority": 1, "is_deleted": 0, "additional_field": {"id": 25, "vem_num": "VEM-25", "aevm_num": "AEVM-25", "atg_num": "ATG-25", "start_time": "2025-01-26 10:00:00", "end_time": "2025-01-26 12:00:00"}, "ticket_users": [{"id": 49, "user": {"id": 101, "name": "user.101", "first_name": "First101", "last_name": "Last101"}, "type": 1, "ticket_id": 25}, {"id": 50, "user": {"id": 201, "name": "tech.201", "first_name": "TechFirst201", "last_name": "TechLast201"}, "type": 2, "ticket_id": 25}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 26, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 26: Issue with Service 2", "date": "2025-01-27T10:00:00", "date_modified": "2025-01-27T11:00:00", "open_date": "2025-01-27T10:00:00", "close_date": "2025-01-28T10:00:00", "solve_date": "2025-01-31T10:00:00", "status": 6, "content": "Detailed description for sample ticket 26. This is a test case.", "urgency": 3, "impact": 3, "priority": 2, "is_deleted": 0, "additional_field": {"id": 26, "vem_num": "VEM-26", "aevm_num": "AEVM-26", "atg_num": "ATG-26", "start_time": "2025-01-27 10:00:00", "end_time": "2025-01-27 12:00:00"}, "ticket_users": [{"id": 51, "user": {"id": 102, "name": "user.102", "first_name": "First102", "last_name": "Last102"}, "type": 1, "ticket_id": 26}, {"id": 52, "user": {"id": 202, "name": "tech.202", "first_name": "TechFirst202", "last_name": "TechLast202"}, "type": 2, "ticket_id": 26}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": 168.0}, {"id": 27, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 27: Issue with Service 3", "date": "2025-01-28T10:00:00", "date_modified": "2025-01-28T11:00:00", "open_date": "2025-01-28T10:00:00", "close_date": "2025-01-29T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 27. This is a test case.", "urgency": 4, "impact": 1, "priority": 3, "is_deleted": 0, "additional_field": {"id": 27, "vem_num": "VEM-27", "aevm_num": "AEVM-27", "atg_num": "ATG-27", "start_time": "2025-01-28 10:00:00", "end_time": "2025-01-28 12:00:00"}, "ticket_users": [{"id": 53, "user": {"id": 103, "name": "user.103", "first_name": "First103", "last_name": "Last103"}, "type": 1, "ticket_id": 27}, {"id": 54, "user": {"id": 203, "name": "tech.203", "first_name": "TechFirst203", "last_name": "TechLast203"}, "type": 2, "ticket_id": 27}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 28, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 28: Issue with Service 4", "date": "2025-01-29T10:00:00", "date_modified": "2025-01-29T11:00:00", "open_date": "2025-01-29T10:00:00", "close_date": "2025-01-30T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 28. This is a test case.", "urgency": 1, "impact": 2, "priority": 4, "is_deleted": 0, "additional_field": {"id": 28, "vem_num": "VEM-28", "aevm_num": "AEVM-28", "atg_num": "ATG-28", "start_time": "2025-01-29 10:00:00", "end_time": "2025-01-29 12:00:00"}, "ticket_users": [{"id": 55, "user": {"id": 104, "name": "user.104", "first_name": "First104", "last_name": "Last104"}, "type": 1, "ticket_id": 28}, {"id": 56, "user": {"id": 204, "name": "tech.204", "first_name": "TechFirst204", "last_name": "TechLast204"}, "type": 2, "ticket_id": 28}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 29, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 29: Issue with Service 5", "date": "2025-01-30T10:00:00", "date_modified": "2025-01-30T11:00:00", "open_date": "2025-01-30T10:00:00", "close_date": "2025-01-31T10:00:00", "solve_date": "2025-01-31T10:00:00", "status": 6, "content": "Detailed description for sample ticket 29. This is a test case.", "urgency": 2, "impact": 1, "priority": 5, "is_deleted": 0, "additional_field": {"id": 29, "vem_num": "VEM-29", "aevm_num": "AEVM-29", "atg_num": "ATG-29", "start_time": "2025-01-30 10:00:00", "end_time": "2025-01-30 12:00:00"}, "ticket_users": [{"id": 57, "user": {"id": 105, "name": "user.105", "first_name": "First105", "last_name": "Last105"}, "type": 1, "ticket_id": 29}, {"id": 58, "user": {"id": 205, "name": "tech.205", "first_name": "TechFirst205", "last_name": "TechLast205"}, "type": 2, "ticket_id": 29}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": 168.0}, {"id": 30, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 30: Issue with Service 1", "date": "2025-01-31T10:00:00", "date_modified": "2025-01-31T11:00:00", "open_date": "2025-01-31T10:00:00", "close_date": "2025-02-01T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 30. This is a test case.", "urgency": 3, "impact": 2, "priority": 1, "is_deleted": 0, "additional_field": {"id": 30, "vem_num": "VEM-30", "aevm_num": "AEVM-30", "atg_num": "ATG-30", "start_time": "2025-01-31 10:00:00", "end_time": "2025-01-31 12:00:00"}, "ticket_users": [{"id": 59, "user": {"id": 101, "name": "user.101", "first_name": "First101", "last_name": "Last101"}, "type": 1, "ticket_id": 30}, {"id": 60, "user": {"id": 201, "name": "tech.201", "first_name": "TechFirst201", "last_name": "TechLast201"}, "type": 2, "ticket_id": 30}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 31, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 31: Issue with Service 2", "date": "2025-02-01T10:00:00", "date_modified": "2025-02-01T11:00:00", "open_date": "2025-02-01T10:00:00", "close_date": "2025-02-02T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 31. This is a test case.", "urgency": 2, "impact": 3, "priority": 2, "is_deleted": 0, "additional_field": {"id": 31, "vem_num": "VEM-31", "aevm_num": "AEVM-31", "atg_num": "ATG-31", "start_time": "2025-02-01 10:00:00", "end_time": "2025-02-01 12:00:00"}, "ticket_users": [{"id": 61, "user": {"id": 102, "name": "user.102", "first_name": "First102", "last_name": "Last102"}, "type": 1, "ticket_id": 31}, {"id": 62, "user": {"id": 202, "name": "tech.202", "first_name": "TechFirst202", "last_name": "TechLast202"}, "type": 2, "ticket_id": 31}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 32, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 32: Issue with Service 3", "date": "2025-02-02T10:00:00", "date_modified": "2025-02-02T11:00:00", "open_date": "2025-02-02T10:00:00", "close_date": "2025-02-03T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 32. This is a test case.", "urgency": 3, "impact": 2, "priority": 3, "is_deleted": 0, "additional_field": {"id": 32, "vem_num": "VEM-32", "aevm_num": "AEVM-32", "atg_num": "ATG-32", "start_time": "2025-02-02 10:00:00", "end_time": "2025-02-02 12:00:00"}, "ticket_users": [{"id": 63, "user": {"id": 103, "name": "user.103", "first_name": "First103", "last_name": "Last103"}, "type": 1, "ticket_id": 32}, {"id": 64, "user": {"id": 203, "name": "tech.203", "first_name": "TechFirst203", "last_name": "TechLast203"}, "type": 2, "ticket_id": 32}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 33, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 33: Issue with Service 4", "date": "2025-02-03T10:00:00", "date_modified": "2025-02-03T11:00:00", "open_date": "2025-02-03T10:00:00", "close_date": "2025-02-04T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 33. This is a test case.", "urgency": 4, "impact": 3, "priority": 4, "is_deleted": 0, "additional_field": {"id": 33, "vem_num": "VEM-33", "aevm_num": "AEVM-33", "atg_num": "ATG-33", "start_time": "2025-02-03 10:00:00", "end_time": "2025-02-03 12:00:00"}, "ticket_users": [{"id": 65, "user": {"id": 104, "name": "user.104", "first_name": "First104", "last_name": "Last104"}, "type": 1, "ticket_id": 33}, {"id": 66, "user": {"id": 204, "name": "tech.204", "first_name": "TechFirst204", "last_name": "TechLast204"}, "type": 2, "ticket_id": 33}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 34, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 34: Issue with Service 5", "date": "2025-02-04T10:00:00", "date_modified": "2025-02-04T11:00:00", "open_date": "2025-02-04T10:00:00", "close_date": "2025-02-05T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 34. This is a test case.", "urgency": 1, "impact": 3, "priority": 5, "is_deleted": 0, "additional_field": {"id": 34, "vem_num": "VEM-34", "aevm_num": "AEVM-34", "atg_num": "ATG-34", "start_time": "2025-02-04 10:00:00", "end_time": "2025-02-04 12:00:00"}, "ticket_users": [{"id": 67, "user": {"id": 105, "name": "user.105", "first_name": "First105", "last_name": "Last105"}, "type": 1, "ticket_id": 34}, {"id": 68, "user": {"id": 205, "name": "tech.205", "first_name": "TechFirst205", "last_name": "TechLast205"}, "type": 2, "ticket_id": 34}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 35, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 35: Issue with Service 1", "date": "2025-02-05T10:00:00", "date_modified": "2025-02-05T11:00:00", "open_date": "2025-02-05T10:00:00", "close_date": "2025-02-06T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 35. This is a test case.", "urgency": 2, "impact": 3, "priority": 1, "is_deleted": 0, "additional_field": {"id": 35, "vem_num": "VEM-35", "aevm_num": "AEVM-35", "atg_num": "ATG-35", "start_time": "2025-02-05 10:00:00", "end_time": "2025-02-05 12:00:00"}, "ticket_users": [{"id": 69, "user": {"id": 101, "name": "user.101", "first_name": "First101", "last_name": "Last101"}, "type": 1, "ticket_id": 35}, {"id": 70, "user": {"id": 201, "name": "tech.201", "first_name": "TechFirst201", "last_name": "TechLast201"}, "type": 2, "ticket_id": 35}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 36, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 36: Issue with Service 2", "date": "2025-02-06T10:00:00", "date_modified": "2025-02-06T11:00:00", "open_date": "2025-02-06T10:00:00", "close_date": "2025-02-07T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 36. This is a test case.", "urgency": 1, "impact": 2, "priority": 2, "is_deleted": 0, "additional_field": {"id": 36, "vem_num": "VEM-36", "aevm_num": "AEVM-36", "atg_num": "ATG-36", "start_time": "2025-02-06 10:00:00", "end_time": "2025-02-06 12:00:00"}, "ticket_users": [{"id": 71, "user": {"id": 102, "name": "user.102", "first_name": "First102", "last_name": "Last102"}, "type": 1, "ticket_id": 36}, {"id": 72, "user": {"id": 202, "name": "tech.202", "first_name": "TechFirst202", "last_name": "TechLast202"}, "type": 2, "ticket_id": 36}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 37, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 37: Issue with Service 3", "date": "2025-02-07T10:00:00", "date_modified": "2025-02-07T11:00:00", "open_date": "2025-02-07T10:00:00", "close_date": "2025-02-08T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 37. This is a test case.", "urgency": 2, "impact": 3, "priority": 3, "is_deleted": 0, "additional_field": {"id": 37, "vem_num": "VEM-37", "aevm_num": "AEVM-37", "atg_num": "ATG-37", "start_time": "2025-02-07 10:00:00", "end_time": "2025-02-07 12:00:00"}, "ticket_users": [{"id": 73, "user": {"id": 103, "name": "user.103", "first_name": "First103", "last_name": "Last103"}, "type": 1, "ticket_id": 37}, {"id": 74, "user": {"id": 203, "name": "tech.203", "first_name": "TechFirst203", "last_name": "TechLast203"}, "type": 2, "ticket_id": 37}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 38, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 38: Issue with Service 4", "date": "2025-02-08T10:00:00", "date_modified": "2025-02-08T11:00:00", "open_date": "2025-02-08T10:00:00", "close_date": "2025-02-09T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 38. This is a test case.", "urgency": 3, "impact": 1, "priority": 4, "is_deleted": 0, "additional_field": {"id": 38, "vem_num": "VEM-38", "aevm_num": "AEVM-38", "atg_num": "ATG-38", "start_time": "2025-02-08 10:00:00", "end_time": "2025-02-08 12:00:00"}, "ticket_users": [{"id": 75, "user": {"id": 104, "name": "user.104", "first_name": "First104", "last_name": "Last104"}, "type": 1, "ticket_id": 38}, {"id": 76, "user": {"id": 204, "name": "tech.204", "first_name": "TechFirst204", "last_name": "TechLast204"}, "type": 2, "ticket_id": 38}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 39, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 39: Issue with Service 5", "date": "2025-02-09T10:00:00", "date_modified": "2025-02-09T11:00:00", "open_date": "2025-02-09T10:00:00", "close_date": "2025-02-10T10:00:00", "solve_date": "2025-02-16T10:00:00", "status": 6, "content": "Detailed description for sample ticket 39. This is a test case.", "urgency": 2, "impact": 1, "priority": 5, "is_deleted": 0, "additional_field": {"id": 39, "vem_num": "VEM-39", "aevm_num": "AEVM-39", "atg_num": "ATG-39", "start_time": "2025-02-09 10:00:00", "end_time": "2025-02-09 12:00:00"}, "ticket_users": [{"id": 77, "user": {"id": 105, "name": "user.105", "first_name": "First105", "last_name": "Last105"}, "type": 1, "ticket_id": 39}, {"id": 78, "user": {"id": 205, "name": "tech.205", "first_name": "TechFirst205", "last_name": "TechLast205"}, "type": 2, "ticket_id": 39}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": 168.0}, {"id": 40, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 40: Issue with Service 1", "date": "2025-02-10T10:00:00", "date_modified": "2025-02-10T11:00:00", "open_date": "2025-02-10T10:00:00", "close_date": "2025-02-11T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 40. This is a test case.", "urgency": 3, "impact": 2, "priority": 1, "is_deleted": 0, "additional_field": {"id": 40, "vem_num": "VEM-40", "aevm_num": "AEVM-40", "atg_num": "ATG-40", "start_time": "2025-02-10 10:00:00", "end_time": "2025-02-10 12:00:00"}, "ticket_users": [{"id": 79, "user": {"id": 101, "name": "user.101", "first_name": "First101", "last_name": "Last101"}, "type": 1, "ticket_id": 40}, {"id": 80, "user": {"id": 201, "name": "tech.201", "first_name": "TechFirst201", "last_name": "TechLast201"}, "type": 2, "ticket_id": 40}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 41, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 41: Issue with Service 2", "date": "2025-02-11T10:00:00", "date_modified": "2025-02-11T11:00:00", "open_date": "2025-02-11T10:00:00", "close_date": "2025-02-12T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 41. This is a test case.", "urgency": 4, "impact": 3, "priority": 2, "is_deleted": 0, "additional_field": {"id": 41, "vem_num": "VEM-41", "aevm_num": "AEVM-41", "atg_num": "ATG-41", "start_time": "2025-02-11 10:00:00", "end_time": "2025-02-11 12:00:00"}, "ticket_users": [{"id": 81, "user": {"id": 102, "name": "user.102", "first_name": "First102", "last_name": "Last102"}, "type": 1, "ticket_id": 41}, {"id": 82, "user": {"id": 202, "name": "tech.202", "first_name": "TechFirst202", "last_name": "TechLast202"}, "type": 2, "ticket_id": 41}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 42, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 42: Issue with Service 3", "date": "2025-02-12T10:00:00", "date_modified": "2025-02-12T11:00:00", "open_date": "2025-02-12T10:00:00", "close_date": "2025-02-13T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 42. This is a test case.", "urgency": 1, "impact": 3, "priority": 3, "is_deleted": 0, "additional_field": {"id": 42, "vem_num": "VEM-42", "aevm_num": "AEVM-42", "atg_num": "ATG-42", "start_time": "2025-02-12 10:00:00", "end_time": "2025-02-12 12:00:00"}, "ticket_users": [{"id": 83, "user": {"id": 103, "name": "user.103", "first_name": "First103", "last_name": "Last103"}, "type": 1, "ticket_id": 42}, {"id": 84, "user": {"id": 203, "name": "tech.203", "first_name": "TechFirst203", "last_name": "TechLast203"}, "type": 2, "ticket_id": 42}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 43, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 43: Issue with Service 4", "date": "2025-02-13T10:00:00", "date_modified": "2025-02-13T11:00:00", "open_date": "2025-02-13T10:00:00", "close_date": "2025-02-14T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 43. This is a test case.", "urgency": 2, "impact": 4, "priority": 4, "is_deleted": 0, "additional_field": {"id": 43, "vem_num": "VEM-43", "aevm_num": "AEVM-43", "atg_num": "ATG-43", "start_time": "2025-02-13 10:00:00", "end_time": "2025-02-13 12:00:00"}, "ticket_users": [{"id": 85, "user": {"id": 104, "name": "user.104", "first_name": "First104", "last_name": "Last104"}, "type": 1, "ticket_id": 43}, {"id": 86, "user": {"id": 204, "name": "tech.204", "first_name": "TechFirst204", "last_name": "TechLast204"}, "type": 2, "ticket_id": 43}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 44, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 44: Issue with Service 5", "date": "2025-02-14T10:00:00", "date_modified": "2025-02-14T11:00:00", "open_date": "2025-02-14T10:00:00", "close_date": "2025-02-15T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 44. This is a test case.", "urgency": 3, "impact": 1, "priority": 5, "is_deleted": 0, "additional_field": {"id": 44, "vem_num": "VEM-44", "aevm_num": "AEVM-44", "atg_num": "ATG-44", "start_time": "2025-02-14 10:00:00", "end_time": "2025-02-14 12:00:00"}, "ticket_users": [{"id": 87, "user": {"id": 105, "name": "user.105", "first_name": "First105", "last_name": "Last105"}, "type": 1, "ticket_id": 44}, {"id": 88, "user": {"id": 205, "name": "tech.205", "first_name": "TechFirst205", "last_name": "TechLast205"}, "type": 2, "ticket_id": 44}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 45, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 45: Issue with Service 1", "date": "2025-02-15T10:00:00", "date_modified": "2025-02-15T11:00:00", "open_date": "2025-02-15T10:00:00", "close_date": "2025-02-16T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 45. This is a test case.", "urgency": 4, "impact": 1, "priority": 1, "is_deleted": 0, "additional_field": {"id": 45, "vem_num": "VEM-45", "aevm_num": "AEVM-45", "atg_num": "ATG-45", "start_time": "2025-02-15 10:00:00", "end_time": "2025-02-15 12:00:00"}, "ticket_users": [{"id": 89, "user": {"id": 101, "name": "user.101", "first_name": "First101", "last_name": "Last101"}, "type": 1, "ticket_id": 45}, {"id": 90, "user": {"id": 201, "name": "tech.201", "first_name": "TechFirst201", "last_name": "TechLast201"}, "type": 2, "ticket_id": 45}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 46, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 46: Issue with Service 2", "date": "2025-02-16T10:00:00", "date_modified": "2025-02-16T11:00:00", "open_date": "2025-02-16T10:00:00", "close_date": "2025-02-17T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 46. This is a test case.", "urgency": 1, "impact": 2, "priority": 2, "is_deleted": 0, "additional_field": {"id": 46, "vem_num": "VEM-46", "aevm_num": "AEVM-46", "atg_num": "ATG-46", "start_time": "2025-02-16 10:00:00", "end_time": "2025-02-16 12:00:00"}, "ticket_users": [{"id": 91, "user": {"id": 102, "name": "user.102", "first_name": "First102", "last_name": "Last102"}, "type": 1, "ticket_id": 46}, {"id": 92, "user": {"id": 202, "name": "tech.202", "first_name": "TechFirst202", "last_name": "TechLast202"}, "type": 2, "ticket_id": 46}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 47, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 47: Issue with Service 3", "date": "2025-02-17T10:00:00", "date_modified": "2025-02-17T11:00:00", "open_date": "2025-02-17T10:00:00", "close_date": "2025-02-18T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 47. This is a test case.", "urgency": 2, "impact": 3, "priority": 3, "is_deleted": 0, "additional_field": {"id": 47, "vem_num": "VEM-47", "aevm_num": "AEVM-47", "atg_num": "ATG-47", "start_time": "2025-02-17 10:00:00", "end_time": "2025-02-17 12:00:00"}, "ticket_users": [{"id": 93, "user": {"id": 103, "name": "user.103", "first_name": "First103", "last_name": "Last103"}, "type": 1, "ticket_id": 47}, {"id": 94, "user": {"id": 203, "name": "tech.203", "first_name": "TechFirst203", "last_name": "TechLast203"}, "type": 2, "ticket_id": 47}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 48, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 48: Issue with Service 4", "date": "2025-02-18T10:00:00", "date_modified": "2025-02-18T11:00:00", "open_date": "2025-02-18T10:00:00", "close_date": "2025-02-19T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 48. This is a test case.", "urgency": 3, "impact": 1, "priority": 4, "is_deleted": 0, "additional_field": {"id": 48, "vem_num": "VEM-48", "aevm_num": "AEVM-48", "atg_num": "ATG-48", "start_time": "2025-02-18 10:00:00", "end_time": "2025-02-18 12:00:00"}, "ticket_users": [{"id": 95, "user": {"id": 104, "name": "user.104", "first_name": "First104", "last_name": "Last104"}, "type": 1, "ticket_id": 48}, {"id": 96, "user": {"id": 204, "name": "tech.204", "first_name": "TechFirst204", "last_name": "TechLast204"}, "type": 2, "ticket_id": 48}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 49, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 49: Issue with Service 5", "date": "2025-02-19T10:00:00", "date_modified": "2025-02-19T11:00:00", "open_date": "2025-02-19T10:00:00", "close_date": "2025-02-20T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 49. This is a test case.", "urgency": 4, "impact": 2, "priority": 5, "is_deleted": 0, "additional_field": {"id": 49, "vem_num": "VEM-49", "aevm_num": "AEVM-49", "atg_num": "ATG-49", "start_time": "2025-02-19 10:00:00", "end_time": "2025-02-19 12:00:00"}, "ticket_users": [{"id": 97, "user": {"id": 105, "name": "user.105", "first_name": "First105", "last_name": "Last105"}, "type": 1, "ticket_id": 49}, {"id": 98, "user": {"id": 205, "name": "tech.205", "first_name": "TechFirst205", "last_name": "TechLast205"}, "type": 2, "ticket_id": 49}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}, {"id": 50, "entity": {"id": 1, "name": "TRG", "complete_name": "TRG", "comment": "Comment for TRG Entity"}, "name": "Sample Ticket 50: Issue with Service 1", "date": "2025-02-20T10:00:00", "date_modified": "2025-02-20T11:00:00", "open_date": "2025-02-20T10:00:00", "close_date": "2025-02-21T10:00:00", "solve_date": null, "status": 1, "content": "Detailed description for sample ticket 50. This is a test case.", "urgency": 1, "impact": 3, "priority": 1, "is_deleted": 0, "additional_field": {"id": 50, "vem_num": "VEM-50", "aevm_num": "AEVM-50", "atg_num": "ATG-50", "start_time": "2025-02-20 10:00:00", "end_time": "2025-02-20 12:00:00"}, "ticket_users": [{"id": 99, "user": {"id": 101, "name": "user.101", "first_name": "First101", "last_name": "Last101"}, "type": 1, "ticket_id": 50}, {"id": 100, "user": {"id": 201, "name": "tech.201", "first_name": "TechFirst201", "last_name": "TechLast201"}, "type": 2, "ticket_id": 50}], "itil_category": {"id": 1, "is_recursive": 1, "name": "General IT Support", "complete_name": "Root > General IT Support", "comment": "General IT Support Category"}, "duration": null}]