from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from config import settings

glpi_engine = create_engine(
    settings.sqlalchemy_string, 
    # connect_args={'sslmode':'require'}
    pool_recycle=3600,
    pool_pre_ping=True
)

GlpiSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=glpi_engine)

def get_glpi_db():
    db = GlpiSessionLocal()
    try:
        yield db
    finally:
        db.close() 


app_engine = create_engine(
    settings.sqlalchemy2_string, 
    # connect_args={'sslmode':'require'}
    pool_recycle=3600,
    pool_pre_ping=True
)

AppSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=app_engine)

def get_app_db():
    db = AppSessionLocal()
    try:
        yield db
    finally:
        db.close() 


Base = declarative_base()        