import sys
import os
import types
import pytest
from fastapi.testclient import TestClient
from datetime import datetime, timedelta
from fastapi import Fast<PERSON><PERSON>

# Ensure project root is in sys.path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# Patch database.get_glpi_db before importing router
mock_database = types.ModuleType('database')
def dummy_get_glpi_db():
    return None
mock_database.get_glpi_db = dummy_get_glpi_db
mock_database.Base = object  # Patch Base for model import
sys.modules['database'] = mock_database

from lib.routers.glpi.tickettask import router

# Mock models
import lib.models.glpi as model

class DummyUser:
    id = 1  # <-- Add this
    name = "Test User"  # <-- And this
    
    def __init__(self, id=1, name="Test User"):
        self.id = id
        self.name = name

class DummyTicketTask:
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)
        self.ticket = None
        self.duration_hr = kwargs.get('duration', 0) / 3600.0
        self.id = 123

class DummyLog:
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

class DummyEvent:
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

model.TicketTask = DummyTicketTask
model.Log = DummyLog
model.Event = DummyEvent
model.User = DummyUser

# Dummy DB session
class DummySession:
    def __init__(self):
        self.added = []
        self.committed = False
        self.refreshed = []
    def add(self, obj):
        self.added.append(obj)
    def commit(self):
        self.committed = True
    def refresh(self, obj):
        self.refreshed.append(obj)
    def query(self, model_class):
        class DummyQuery:
            def filter(self, *args, **kwargs):
                return self
            def first(self):
                if model_class == model.User:
                    return DummyUser(id=1, name="Test User")
                return None
        return DummyQuery()

# Patch schemas
import lib.schemas.glpi as schemas
class DummyTicketTaskSchema:
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)
    def dict(self):
        return self.__dict__
schemas.TicketTask = DummyTicketTaskSchema

# FastAPI app
app = FastAPI()
app.include_router(router)

from lib.auth import get_current_user
from database import get_glpi_db

@pytest.fixture
def client():
    return TestClient(app)

def test_add_ticket_task_success(client):
    def fake_get_current_user():
        return {'id': 1, 'name': 'Test User'}
    def fake_get_glpi_db():
        return DummySession()

    app.dependency_overrides[get_current_user] = fake_get_current_user
    app.dependency_overrides[get_glpi_db] = fake_get_glpi_db

    now = datetime.now()
    response = client.post(
        "/add_task",
        params={
            "tickets_id": 1,
            "content": "Test task content",
            "start_date": now.isoformat(),
            "end_date": (now + timedelta(hours=2)).isoformat(),
            "state": 1,
            "groups_id_TicketTask": 1
        }
    )

    assert response.status_code == 200
    data = response.json()
    assert data["ticket_id"] == 1
    assert data["content"] == "Test task content"
    assert data["user"]["name"] == "Test User"

def test_add_ticket_task_unauthenticated(client):
    def fake_get_current_user():
        return None
    def fake_get_glpi_db():
        return DummySession()

    app.dependency_overrides = {
        get_current_user: fake_get_current_user,
        get_glpi_db: fake_get_glpi_db
    }

    now = datetime.now()
    response = client.post(
        "/add_task",
        params={
            "tickets_id": 1,
            "content": "Test task content",
            "start_date": now.isoformat(),
            "end_date": (now + timedelta(hours=2)).isoformat(),
            "state": 1,
            "groups_id_TicketTask": 1
        }
    )

    assert response.status_code == 401
    assert response.json()["detail"] == "User authentication required"

def override_get_current_user():
    return {"id": 1, "name": "Test User"}

app.dependency_overrides[get_current_user] = override_get_current_user
