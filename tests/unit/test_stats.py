import pytest
from fastapi.testclient import TestClient
from datetime import datetime
import os

# Set the TESTING environment variable before importing main
os.environ["TESTING"] = "True"

from main import app  # Assuming your FastAPI app is in main.py
from database import get_glpi_db # This will now be the real dependency
from lib.auth import get_current_user
from lib.models import glpi as model # Still used for model definitions
from lib.schemas import glpi as schemas # Still used for schema definitions

client = TestClient(app)

# We'll keep override_get_current_user for now, but mock_db_session and setup_overrides will be removed.

def override_get_current_user():
    # This mock is kept as it's for authentication, not the DB itself.
    return {"username": "testuser", "id": 1} # A dummy user for authentication

@pytest.fixture(autouse=True)
def setup_overrides():
    # We no longer override get_glpi_db here, so the actual DB connection is used.
    app.dependency_overrides[get_current_user] = override_get_current_user
    yield
    app.dependency_overrides.clear()

def test_get_all_ticket_stats_basic():
    start_date = "2025-01-01T00:00:00Z"
    end_date = "2025-01-31T23:59:59Z"
    itil_category_id = 1
    entity_id = 1

    response = client.get(
        f"/stats/all_tickets?itil_category_id={itil_category_id}&start_date={start_date}&end_date={end_date}&entity_id={entity_id}"
    )

    assert response.status_code == 200
    data = response.json()
    print(f"\nOutput for test_get_all_ticket_stats_basic:\n{data}")

    # These assertions will need to be updated based on your actual MariaDB data.
    # We are now checking the structure and non-negative counts instead of specific values.
    assert isinstance(data, list)
    for group_stats in data:
        assert "group" in group_stats
        assert "id" in group_stats["group"]
        assert "name" in group_stats["group"]
        assert "openTicketsAtStart" in group_stats
        assert "closedTicketsDuringRange" in group_stats
        assert "newTicketsDuringRange" in group_stats
        assert "openTicketsAtEnd" in group_stats

        assert isinstance(group_stats["openTicketsAtStart"]["count"], int) and group_stats["openTicketsAtStart"]["count"] >= 0
        assert isinstance(group_stats["closedTicketsDuringRange"]["count"], int) and group_stats["closedTicketsDuringRange"]["count"] >= 0
        assert isinstance(group_stats["newTicketsDuringRange"]["count"], int) and group_stats["newTicketsDuringRange"]["count"] >= 0
        assert isinstance(group_stats["openTicketsAtEnd"]["count"], int) and group_stats["openTicketsAtEnd"]["count"] >= 0

        assert isinstance(group_stats["openTicketsAtStart"]["tickets"], list)
        assert isinstance(group_stats["closedTicketsDuringRange"]["tickets"], list)
        assert isinstance(group_stats["newTicketsDuringRange"]["tickets"], list)
        assert isinstance(group_stats["openTicketsAtEnd"]["tickets"], list)

def test_get_no_group_ticket_stats():
    start_date = "2025-01-01T00:00:00Z"
    end_date = "2025-12-31T23:59:59Z" 
    itil_category_id = 1
    entity_id = 1

    response = client.get(
        f"/stats/no_group_tickets?itil_category_id={itil_category_id}&start_date={start_date}&end_date={end_date}&entity_id={entity_id}"
    )

    assert response.status_code == 200
    data = response.json()
    print(f"\nOutput for test_get_no_group_ticket_stats:\n{data}")

    # These assertions will need to be updated based on your actual MariaDB data.
    # We are now checking the structure and non-negative counts instead of specific values.
    assert "openTicketsAtStart" in data
    assert "closedTicketsDuringRange" in data
    assert "newTicketsDuringRange" in data
    assert "openTicketsAtEnd" in data

    assert isinstance(data["openTicketsAtStart"]["count"], int) and data["openTicketsAtStart"]["count"] >= 0
    assert isinstance(data["closedTicketsDuringRange"]["count"], int) and data["closedTicketsDuringRange"]["count"] >= 0
    assert isinstance(data["newTicketsDuringRange"]["count"], int) and data["newTicketsDuringRange"]["count"] >= 0
    assert isinstance(data["openTicketsAtEnd"]["count"], int) and data["openTicketsAtEnd"]["count"] >= 0

    assert isinstance(data["openTicketsAtStart"]["tickets"], list)
    assert isinstance(data["closedTicketsDuringRange"]["tickets"], list)
    assert isinstance(data["newTicketsDuringRange"]["tickets"], list)
    assert isinstance(data["openTicketsAtEnd"]["tickets"], list)

def test_get_ticket_timeframe_counts():
    response = client.get(
        f"/stats/ticket-timeframe-counts"
    )

    assert response.status_code == 200
    data = response.json()
    print(f"\nOutput for test_get_ticket_timeframe_counts:\n{data}")

    assert isinstance(data, list)
    assert len(data) > 0 # Assuming there's at least one group

    for group_data in data:
        assert "group" in group_data
        assert "counts" in group_data

        assert "id" in group_data["group"]
        assert "name" in group_data["group"]
        # Add more assertions for group fields if needed

        counts = group_data["counts"]
        assert "today" in counts
        assert "past_7_days" in counts
        assert "past_8_to_14_days" in counts
        assert "past_15_to_30_days" in counts
        assert "past_31_to_60_days" in counts
        assert "past_61_to_90_days" in counts
        assert "past_91_days_to_6_months" in counts
        assert "past_6_months_to_1_year" in counts
        assert "past_1_to_2_years" in counts
        assert "older_than_2_years" in counts

        # Assert counts are non-negative integers
        for key, value in counts.items():
            assert isinstance(value, int)
            assert value >= 0

def test_get_group_ticket_details_by_timeframe():
    group_id = 8

    response = client.get(
        f"/stats/group/{group_id}/ticket-details-by-timeframe"
    )

    assert response.status_code == 200
    data = response.json()
    print(f"\nOutput for test_get_group_ticket_details_by_timeframe:\n{data}")

    assert "group" in data
    assert "details" in data

    assert "id" in data["group"]
    assert "name" in data["group"]
    # Add more assertions for group fields if needed

    details = data["details"]
    assert "today_tickets" in details
    assert "past_7_days_tickets" in details
    assert "past_8_to_14_days_tickets" in details
    assert "past_15_to_30_days_tickets" in details
    assert "past_31_to_60_days_tickets" in details
    assert "past_61_to_90_days_tickets" in details
    assert "past_91_days_to_6_months_tickets" in details
    assert "past_6_months_to_1_year_tickets" in details
    assert "past_1_to_2_years_tickets" in details
    assert "older_than_2_years_tickets" in details

    # Assert that each list of tickets contains actual Ticket objects
    for key, ticket_list in details.items():
        assert isinstance(ticket_list, list)
        for ticket in ticket_list:
            # Basic checks for a ticket object
            assert "id" in ticket
            assert "name" in ticket
            assert "date" in ticket

def test_get_group_ticket_details_by_timeframe_with_timeframe_param():
    group_id = 8
    timeframe = 2 # Example valid timeframe, corresponds to "past_8_to_14_days"

    response = client.get(
        f"/stats/group/{group_id}/ticket-details-by-timeframe?timeframe={timeframe}"
    )

    assert response.status_code == 200
    data = response.json()
    print(f"\nOutput for test_get_group_ticket_details_by_timeframe_with_timeframe_param (valid timeframe):\n{data}")

    assert "group" in data
    assert "details" in data

    # Expected key based on the integer timeframe mapping in the router (2 maps to "past_8_to_14_days_tickets")
    expected_timeframe_key = "past_8_to_14_days_tickets"

    # All possible timeframe keys (from the router's VALID_TIMEFRAME_MAPPING)
    all_possible_timeframe_keys = [
        "today_tickets", "past_7_days_tickets", "past_8_to_14_days_tickets",
        "past_15_to_30_days_tickets", "past_31_to_60_days_tickets",
        "past_61_to_90_days_tickets", "past_91_days_to_6_months_tickets",
        "past_6_months_to_1_year_tickets", "past_1_to_2_years_tickets",
        "older_than_2_years_tickets",
    ]

    for key in all_possible_timeframe_keys:
        if key == expected_timeframe_key:
            assert isinstance(data["details"][key], list)
            for ticket in data["details"][key]:
                assert "id" in ticket
                assert "name" in ticket
                assert "date" in ticket
        else:
            # All other timeframes should be empty lists
            assert data["details"][key] == []

def test_get_group_ticket_details_by_timeframe_invalid_timeframe_param():
    group_id = 8
    invalid_timeframe = 99 # Example invalid integer timeframe

    response = client.get(
        f"/stats/group/{group_id}/ticket-details-by-timeframe?timeframe={invalid_timeframe}"
    )

    assert response.status_code == 400
    data = response.json()
    print(f"\nOutput for test_get_group_ticket_details_by_timeframe_invalid_timeframe_param:\n{data}")

    assert "detail" in data
    assert data["detail"] == "Invalid timeframe provided"

def test_get_inactive_ticket_timeframe_counts():
    response = client.get(
        f"/stats/inactive-ticket-timeframe-counts"
    )

    assert response.status_code == 200
    data = response.json()
    print(f"\nOutput for test_get_inactive_ticket_timeframe_counts:\n{data}")

    assert isinstance(data, list)
    assert len(data) > 0 # Assuming there's at least one group

    for group_data in data:
        assert "group" in group_data
        assert "counts" in group_data

        assert "id" in group_data["group"]
        assert "name" in group_data["group"]
        # Add more assertions for group fields if needed

        counts = group_data["counts"]
        assert "today" in counts
        assert "past_7_days" in counts
        assert "past_8_to_14_days" in counts
        assert "past_15_to_30_days" in counts
        assert "past_31_to_60_days" in counts
        assert "past_61_to_90_days" in counts
        assert "past_91_days_to_6_months" in counts
        assert "past_6_months_to_1_year" in counts
        assert "past_1_to_2_years" in counts
        assert "older_than_2_years" in counts

        # Assert counts are non-negative integers
        for key, value in counts.items():
            assert isinstance(value, int)
            assert value >= 0

def test_get_group_inactive_ticket_details_by_timeframe():
    group_id = 8

    response = client.get(
        f"/stats/group/{group_id}/inactive-ticket-details-by-timeframe"
    )

    assert response.status_code == 200
    data = response.json()
    print(f"\nOutput for test_get_group_inactive_ticket_details_by_timeframe:\n{data}")

    assert "group" in data
    assert "details" in data

    assert "id" in data["group"]
    assert "name" in data["group"]
    # Add more assertions for group fields if needed

    details = data["details"]
    assert "today_tickets" in details
    assert "past_7_days_tickets" in details
    assert "past_8_to_14_days_tickets" in details
    assert "past_15_to_30_days_tickets" in details
    assert "past_31_to_60_days_tickets" in details
    assert "past_61_to_90_days_tickets" in details
    assert "past_91_days_to_6_months_tickets" in details
    assert "past_6_months_to_1_year_tickets" in details
    assert "past_1_to_2_years_tickets" in details
    assert "older_than_2_years_tickets" in details

    # Assert that each list of tickets contains actual Ticket objects
    for key, ticket_list in details.items():
        assert isinstance(ticket_list, list)
        for ticket in ticket_list:
            # Basic checks for a ticket object
            assert "id" in ticket
            assert "name" in ticket
            assert "date" in ticket

def test_get_group_inactive_ticket_details_by_timeframe_with_timeframe_param():
    group_id = 8
    timeframe = 2 # Changed to integer 2, which corresponds to "past_8_to_14_days"

    response = client.get(
        f"/stats/group/{group_id}/inactive-ticket-details-by-timeframe?timeframe={timeframe}"
    )

    assert response.status_code == 200
    data = response.json()
    print(f"\nOutput for test_get_group_inactive_ticket_details_by_timeframe_with_timeframe_param (valid timeframe):\n{data}")

    assert "group" in data
    assert "details" in data

    # Expected key based on the integer timeframe mapping in the router (2 maps to "past_8_to_14_days_tickets")
    expected_timeframe_key = "past_8_to_14_days_tickets"

    # Expect only the requested timeframe to have data, others should be empty lists
    all_possible_timeframe_keys = [
        "today_tickets", "past_7_days_tickets", "past_8_to_14_days_tickets",
        "past_15_to_30_days_tickets", "past_31_to_60_days_tickets",
        "past_61_to_90_days_tickets", "past_91_days_to_6_months_tickets",
        "past_6_months_to_1_year_tickets", "past_1_to_2_years_tickets",
        "older_than_2_years_tickets",
    ]

    for key in all_possible_timeframe_keys:
        if key == expected_timeframe_key:
            assert isinstance(data["details"][key], list)
            for ticket in data["details"][key]:
                assert "id" in ticket
                assert "name" in ticket
                assert "date" in ticket
        else:
            # All other timeframes should be empty lists
            assert data["details"][key] == []

def test_get_group_inactive_ticket_details_by_timeframe_invalid_timeframe_param():
    group_id = 8
    invalid_timeframe = 99 # Changed to an invalid integer

    response = client.get(
        f"/stats/group/{group_id}/inactive-ticket-details-by-timeframe?timeframe={invalid_timeframe}"
    )

    assert response.status_code == 400
    data = response.json()
    print(f"\nOutput for test_get_group_inactive_ticket_details_by_timeframe_invalid_timeframe_param:\n{data}")

    assert "detail" in data
    assert data["detail"] == "Invalid timeframe provided"

def test_health_check():
    response = client.get("/test_health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}

def test_get_ticket_stats_by_group():
    group_id = 1
    itil_category_id = 1
    entity_id = 1
    start_date = "2025-01-01T00:00:00Z"
    end_date = "2025-01-31T23:59:59Z"

    response = client.get(
        f"/stats/group/{group_id}?itil_category_id={itil_category_id}&start_date={start_date}&end_date={end_date}&entity_id={entity_id}"
    )

    assert response.status_code == 200
    data = response.json()
    print(f"\nOutput for test_get_ticket_stats_by_group:\n{data}")

    assert "openTicketsAtStart" in data
    assert "closedTicketsDuringRange" in data
    assert "newTicketsDuringRange" in data
    assert "openTicketsAtEnd" in data

    # These assertions will need to be updated based on your actual MariaDB data.
    # For now, we are just checking the structure and that counts are non-negative.
    assert isinstance(data["openTicketsAtStart"]["count"], int) and data["openTicketsAtStart"]["count"] >= 0
    assert isinstance(data["closedTicketsDuringRange"]["count"], int) and data["closedTicketsDuringRange"]["count"] >= 0
    assert isinstance(data["newTicketsDuringRange"]["count"], int) and data["newTicketsDuringRange"]["count"] >= 0
    assert isinstance(data["openTicketsAtEnd"]["count"], int) and data["openTicketsAtEnd"]["count"] >= 0

    assert isinstance(data["openTicketsAtStart"]["tickets"], list)
    assert isinstance(data["closedTicketsDuringRange"]["tickets"], list)
    assert isinstance(data["newTicketsDuringRange"]["tickets"], list)
    assert isinstance(data["openTicketsAtEnd"]["tickets"], list)