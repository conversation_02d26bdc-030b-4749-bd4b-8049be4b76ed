import os
import sys
import pytest
from unittest.mock import patch, MagicMock

# Ensure project root is in sys.path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# Import the migration module
from lib.utils.migration import run_migrations


def test_run_migrations_success():
    """Test that migrations run successfully when everything is configured correctly."""
    with patch('lib.utils.migration.command') as mock_command, \
         patch('lib.utils.migration.Config') as mock_config, \
         patch('lib.utils.migration.logger') as mock_logger:
        
        # Setup mocks
        mock_config_instance = MagicMock()
        mock_config.return_value = mock_config_instance
        
        # Call the function
        result = run_migrations()
        
        # Verify the function behavior
        assert result is True
        mock_config.assert_called_once()
        mock_config_instance.set_main_option.assert_called_once()
        mock_command.upgrade.assert_called_once_with(mock_config_instance, 'head')
        mock_logger.info.assert_any_call("Running database migrations...")
        mock_logger.info.assert_any_call("Database migrations completed successfully.")


def test_run_migrations_exception():
    """Test that migrations handle exceptions gracefully."""
    with patch('lib.utils.migration.command') as mock_command, \
         patch('lib.utils.migration.Config') as mock_config, \
         patch('lib.utils.migration.logger') as mock_logger:
        
        # Setup mocks to raise an exception
        mock_config_instance = MagicMock()
        mock_config.return_value = mock_config_instance
        mock_command.upgrade.side_effect = Exception("Test exception")
        
        # Call the function
        result = run_migrations()
        
        # Verify the function behavior
        assert result is False
        mock_config.assert_called_once()
        mock_config_instance.set_main_option.assert_called_once()
        mock_command.upgrade.assert_called_once_with(mock_config_instance, 'head')
        mock_logger.error.assert_called_once()
        assert "Test exception" in mock_logger.error.call_args[0][0]


def test_run_migrations_path_resolution():
    """Test that the migration script correctly resolves paths."""
    with patch('lib.utils.migration.command') as mock_command, \
         patch('lib.utils.migration.Config') as mock_config, \
         patch('lib.utils.migration.os.path') as mock_path, \
         patch('lib.utils.migration.logger') as mock_logger:
        
        # Setup mocks for path resolution
        mock_config_instance = MagicMock()
        mock_config.return_value = mock_config_instance
        
        # Mock path resolution
        mock_path.abspath.return_value = "/mock/path/to/file"
        mock_path.dirname.side_effect = [
            "/mock/path/to",  # First call for __file__
            "/mock/path",    # Second call for lib
            "/mock"         # Third call for root
        ]
        mock_path.join.side_effect = lambda *args: "/".join(args)
        
        # Call the function
        result = run_migrations()
        
        # Verify the function behavior
        assert result is True
        mock_config.assert_called_once_with("/mock/alembic.ini")
        mock_config_instance.set_main_option.assert_called_once_with(
            'script_location', '/mock/alembic')