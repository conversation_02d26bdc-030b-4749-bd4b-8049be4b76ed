import os
import sys
import pytest
from unittest.mock import patch, MagicMock

# Ensure project root is in sys.path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))


def test_migrations_run_on_startup():
    """Test that migrations are run during application startup when not in testing mode."""
    # Save the original environment variable
    original_testing_env = os.environ.get("TESTING")
    
    try:
        # Set TESTING to False to ensure migrations run
        os.environ["TESTING"] = "False"
        
        with patch('lib.utils.migration.run_migrations') as mock_run_migrations, \
             patch('lib.models.dar.DarEntry.__table__.create') as mock_dar_entry_create, \
             patch('lib.models.dar.DarSetting.__table__.create') as mock_dar_setting_create, \
             patch('fastapi.FastAPI') as mock_fastapi, \
             patch('fastapi_pagination.add_pagination') as mock_add_pagination, \
             patch('logging.getLogger') as mock_get_logger, \
             patch('logging.StreamHandler') as mock_stream_handler, \
             patch('logging.handlers.RotatingFileHandler') as mock_rotating_handler:
            
            # Mock the logger
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            # Import main module (which will trigger the migrations)
            import main
            
            # Verify migrations were called
            mock_run_migrations.assert_called_once()
            mock_dar_entry_create.assert_called_once()
            mock_dar_setting_create.assert_called_once()
    
    finally:
        # Restore the original environment variable
        if original_testing_env is not None:
            os.environ["TESTING"] = original_testing_env
        else:
            del os.environ["TESTING"]


def test_migrations_not_run_in_testing_mode():
    """Test that migrations are not run during application startup when in testing mode."""
    # Save the original environment variable
    original_testing_env = os.environ.get("TESTING")
    
    try:
        # Set TESTING to True to prevent migrations from running
        os.environ["TESTING"] = "True"
        
        with patch('lib.utils.migration.run_migrations') as mock_run_migrations, \
             patch('lib.models.dar.DarEntry.__table__.create') as mock_dar_entry_create, \
             patch('lib.models.dar.DarSetting.__table__.create') as mock_dar_setting_create, \
             patch('fastapi.FastAPI') as mock_fastapi, \
             patch('fastapi_pagination.add_pagination') as mock_add_pagination, \
             patch('logging.getLogger') as mock_get_logger, \
             patch('logging.StreamHandler') as mock_stream_handler, \
             patch('logging.handlers.RotatingFileHandler') as mock_rotating_handler:
            
            # Mock the logger
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger
            
            # Import main module (which should not trigger migrations in testing mode)
            import main
            
            # Verify migrations were not called
            mock_run_migrations.assert_not_called()
            mock_dar_entry_create.assert_not_called()
            mock_dar_setting_create.assert_not_called()
    
    finally:
        # Restore the original environment variable
        if original_testing_env is not None:
            os.environ["TESTING"] = original_testing_env
        else:
            del os.environ["TESTING"]