import os
import sys
import pytest

# Ensure project root is in sys.path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))


def test_run_migrations_script_exists():
    """Test that the run_migrations.py script exists and is executable."""
    # Get the path to the run_migrations.py script
    script_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'run_migrations.py')
    
    # Check that the script exists
    assert os.path.exists(script_path), f"Script {script_path} does not exist"
    
    # Check that the script is executable
    assert os.access(script_path, os.X_OK), f"Script {script_path} is not executable"
    
    # Check that the script imports the run_migrations function
    with open(script_path, 'r') as f:
        content = f.read()
        assert "from lib.utils.migration import run_migrations" in content, "Script does not import run_migrations function"