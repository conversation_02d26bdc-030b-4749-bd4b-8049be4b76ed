import os
from urllib.parse import quote_plus
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

dbuser = os.getenv('GLPIAPI_DB_USER')
dbhost = os.getenv('GLPIAPI_DB_HOST')
dbport = os.getenv('GLPIAPI_DB_PORT')
dbpwd = quote_plus(str(os.getenv('GLPIAPI_DB_PWD')))
dbname = os.getenv('GLPIAPI_DB_NAME')

appdbuser = os.getenv('GLPIAPI_APPDB_USER')
appdbhost = os.getenv('GLPIAPI_APPDB_HOST')
appdbport = os.getenv('GLPIAPI_APPDB_PORT')
appdbpwd = quote_plus(str(os.getenv('GLPIAPI_APPDB_PWD')))
appdbname = os.getenv('GLPIAPI_APPDB_NAME')


class Settings(BaseSettings):
    sqlalchemy_string: str = f"mysql+pymysql://{dbuser}:{dbpwd}@{dbhost}:{dbport}/{dbname}"
    sqlalchemy2_string: str = f"mysql+pymysql://{appdbuser}:{appdbpwd}@{appdbhost}:{appdbport}/{appdbname}"

settings = Settings()