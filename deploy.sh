#!/bin/bash

# GLPI API Deployment Script
# This script deploys the GLPI API application to production
# with automatic database migrations

set -e

echo "Starting deployment of GLPI API..."

# Pull latest changes
echo "Pulling latest changes from repository..."
git pull

# Build and start the containers
echo "Building and starting containers..."
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# Check if the application is running
echo "Checking if the application is running..."
sleep 5
if curl -s http://localhost:9999/test_health | grep -q '"status":"ok"'; then
    echo "Deployment successful! Application is running."
    echo "API documentation available at:"
    echo "  - Swagger UI: http://localhost:9999/docs"
    echo "  - ReDoc: http://localhost:9999/redoc"
    
    echo "\nDatabase migrations have been automatically applied during startup."
    echo "Check the logs for migration details:"
    echo "docker-compose logs web | grep migration"
    
    exit 0
else
    echo "Deployment failed! Application is not running."
    echo "Check the logs for more information:"
    echo "docker-compose logs web"
    exit 1
fi