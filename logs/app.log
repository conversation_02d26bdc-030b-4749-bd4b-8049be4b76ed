2025-08-02 06:37:43,765 - lib.utils.migration - INFO - Running database migrations...
2025-08-02 06:54:52,466 - httpx - INFO - HTTP Request: GET http://testserver/stats/all_tickets?itil_category_id=1&start_date=2025-01-01T00:00:00Z&end_date=2025-01-31T23:59:59Z&entity_id=1 "HTTP/1.1 200 OK"
2025-08-02 06:54:52,533 - httpx - INFO - HTTP Request: GET http://testserver/stats/no_group_tickets?itil_category_id=1&start_date=2025-01-01T00:00:00Z&end_date=2025-12-31T23:59:59Z&entity_id=1 "HTTP/1.1 422 Unprocessable Entity"
2025-08-02 06:54:52,607 - httpx - INFO - HTTP Request: GET http://testserver/stats/ticket-timeframe-counts "HTTP/1.1 200 OK"
2025-08-02 06:54:52,672 - httpx - INFO - HTTP Request: GET http://testserver/stats/group/8/ticket-details-by-timeframe "HTTP/1.1 200 OK"
2025-08-02 06:54:52,738 - httpx - INFO - HTTP Request: GET http://testserver/stats/group/8/ticket-details-by-timeframe?timeframe=2 "HTTP/1.1 200 OK"
2025-08-02 06:54:52,750 - httpx - INFO - HTTP Request: GET http://testserver/stats/group/8/ticket-details-by-timeframe?timeframe=99 "HTTP/1.1 400 Bad Request"
2025-08-02 06:54:52,831 - httpx - INFO - HTTP Request: GET http://testserver/stats/inactive-ticket-timeframe-counts "HTTP/1.1 200 OK"
2025-08-02 06:54:52,904 - httpx - INFO - HTTP Request: GET http://testserver/stats/group/8/inactive-ticket-details-by-timeframe "HTTP/1.1 200 OK"
2025-08-02 06:54:52,998 - httpx - INFO - HTTP Request: GET http://testserver/stats/group/8/inactive-ticket-details-by-timeframe?timeframe=2 "HTTP/1.1 200 OK"
2025-08-02 06:54:53,003 - httpx - INFO - HTTP Request: GET http://testserver/stats/group/8/inactive-ticket-details-by-timeframe?timeframe=99 "HTTP/1.1 400 Bad Request"
2025-08-02 06:54:53,007 - httpx - INFO - HTTP Request: GET http://testserver/test_health "HTTP/1.1 200 OK"
2025-08-02 06:54:53,014 - httpx - INFO - HTTP Request: GET http://testserver/stats/group/1?itil_category_id=1&start_date=2025-01-01T00:00:00Z&end_date=2025-01-31T23:59:59Z&entity_id=1 "HTTP/1.1 200 OK"
