#!/usr/bin/env python
"""
Standalone script to run database migrations.

This script can be used to manually run database migrations
without starting the full application.

Usage:
    python run_migrations.py
"""

from dotenv import load_dotenv
load_dotenv()

import logging
from lib.utils.migration import run_migrations

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    logger.info("Starting database migration...")
    success = run_migrations()
    if success:
        logger.info("Database migration completed successfully.")
    else:
        logger.error("Database migration failed.")