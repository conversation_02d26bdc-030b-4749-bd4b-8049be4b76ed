version: '3'

services:
  web:
    build: .
    command: sh -c "uvicorn main:app --port 9999 --workers 14 --host=0.0.0.0"
    ports:
      - 9999:9999
    environment:
      - GLPIAPI_DB_USER=root
      - GLPIAPI_DB_PORT=3306
      - GLPIAPI_DB_HOST=************* 
      - GLPIAPI_DB_PWD=M@sunur1nITSM
      - GLPIAPI_DB_NAME=glpidb
      - GLPIAPI_DB2_USER=root
      - GLPIAPI_DB2_HOST=*************
      - GLPIAPI_DB2_PORT=3306
      - GLPIAPI_DB2_PWD=M@sunur1nITSM
      - GLPIAPI_DB2_NAME=appdb
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
