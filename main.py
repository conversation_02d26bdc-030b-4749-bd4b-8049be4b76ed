from dotenv import load_dotenv
load_dotenv()

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi_pagination import add_pagination
from lib.routers.glpi.entity import router as entity_router
from lib.routers.glpi.group import router as group_router
from lib.routers.glpi.ticket import router as ticket_router
from lib.routers.glpi.itilcategory import router as itilcategory_router
from lib.routers.glpi.itilfollowups import router as itilfollowups_router
from lib.routers.glpi.tickettask import router as tickettask_router
from lib.routers.glpi.ticketwithtickettasks import router as ticketwithtickettask_router
from lib.routers.glpi.settings import router as settings_router
from lib.routers.glpi.reports import router as reports_router
from lib.routers.glpi.auth import router as auth_router
from lib.routers.glpi.formanswer import router as formanswer_router
from lib.routers.glpi.stats import router as stats_router

from lib.routers.app.darentry import router as dar_entry_router
from lib.routers.app.darsetting import router as dar_setting_router
from database import app_engine, Base
from lib.models.dar import DarEntry, DarSetting
from lib.utils.migration import run_migrations
import os
import logging
import sys
from logging.handlers import RotatingFileHandler

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, 'app.log')

# Create a logger
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Create handlers
console_handler = logging.StreamHandler(sys.stdout)
file_handler = RotatingFileHandler(log_file, maxBytes=10485760, backupCount=5)  # 10MB per file, max 5 files

# Create formatters
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
file_handler.setFormatter(formatter)

# Add handlers to logger
logger.addHandler(console_handler)
logger.addHandler(file_handler)

# Run database migrations
if os.getenv("TESTING") != "True":
    # Run Alembic migrations
    run_migrations()
    
    # Create DAR tables if they don't exist
    # This is kept for backward compatibility
    DarEntry.__table__.create(app_engine, checkfirst=True)
    DarSetting.__table__.create(app_engine, checkfirst=True)


app = FastAPI(
    title='GLPI API',
    description='GLPI API',
    version='0.1',
    docs_url='/docs',
    redoc_url='/redoc'
)

app.mount('/glpi-reports', StaticFiles(directory='static'), name='static')

origins = [
    'http://localhost:9000',
    'http://127.0.0.1:4000'
]

app.add_middleware(
    CORSMiddleware,
    # allow_origins=origins,
    allow_origins=['*'],
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)

app.include_router(auth_router, prefix='/auth')
app.include_router(entity_router, prefix='/entity')
app.include_router(group_router, prefix='/group')
app.include_router(ticket_router, prefix='/ticket')
app.include_router(itilcategory_router, prefix='/itilcategory')
app.include_router(itilfollowups_router, prefix='/itilfollowup')
app.include_router(tickettask_router, prefix='/tickettask')
app.include_router(ticketwithtickettask_router, prefix='/ticketwithtasks')
app.include_router(formanswer_router, prefix='/formanswer')
app.include_router(reports_router, prefix='/reports')
app.include_router(stats_router, prefix='/stats')
app.include_router(dar_entry_router, prefix='/app/dar')
app.include_router(dar_setting_router, prefix='/app/darsettings')

@app.get("/")

@app.get("/test_health")
async def health_check():
    return {"status": "ok"}

add_pagination(app)