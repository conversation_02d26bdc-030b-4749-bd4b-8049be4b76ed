commit 3dbb770044470d1ee201429ac050dc556f06544e
Author: ambross.tison <<EMAIL>>
Date:   Sun Jul 6 08:10:14 2025 +0000

    add timeframe params to tickets by age and unit test

commit 7a52008ae03f6154364d6b23895beedfb39c8e38
Author: ambross.tison <<EMAIL>>
Date:   Sat Jul 5 10:37:11 2025 +0000

    add timeframe param for inactive ticket details

commit 34ca7be6433d851225e0e8ce23f192388a533e08
Author: ambross.tison <<EMAIL>>
Date:   Sat Jul 5 09:01:36 2025 +0000

    confirm closed tickets being returned

commit cd798c884c9dc7ebfb3a77287a93bd4e86b22f68
Author: ambross.tison <<EMAIL>>
Date:   Sat Jul 5 07:55:51 2025 +0000

    test inactive ticket endpoints

commit e1a344580451a487cefc31903d214e31f58d5de7
Author: ambross.tison <<EMAIL>>
Date:   Sat Jul 5 07:39:46 2025 +0000

    create new endpoint for inactive tickets

commit 4f38287d767f73512a54da590bdad80238bb953d
Author: ambross.tison <<EMAIL>>
Date:   Wed Jul 2 01:47:58 2025 +0000

    log the outputs

commit dd88a6de6d0fe4395e3a0bcedd893a4089b1293d
Author: ambross.tison <<EMAIL>>
Date:   Tue Jul 1 11:24:25 2025 +0000

    add ticket timeframe endpoint tests

commit 26a2e52ebd70e5fd2045b586c2fe4a70f94bb7d8
Author: ambross.tison <<EMAIL>>
Date:   Tue Jul 1 11:13:06 2025 +0000

    add ticket timeframe endpoints

commit 2d64a96c58fcb99ac3b23992048246fa5ee18c2d
Author: ambross.tison <<EMAIL>>
Date:   Sun Jun 29 04:20:16 2025 +0000

    write pytests for stats endpoint

commit b898db7e7c8ac021defc46f894c9cf1f450bfd70
Author: ambross.tison <<EMAIL>>
Date:   Tue Jun 24 10:10:35 2025 +0000

    add endpoint for no group tickets and write test

commit 22e24ce3b41b30af6a975af31848cb59f6481a07
Author: ambross.tison <<EMAIL>>
Date:   Tue Jun 24 04:08:43 2025 +0000

    add ticket stats unit test

commit 6ea7c4715fe3e46435ee0bcd8608d86c61be5e7b
Author: ambross.tison <<EMAIL>>
Date:   Sun Jun 22 09:14:31 2025 +0000

    fix TypeError: can't compare offset-naive and offset-aware datetimes

commit e75e69bebac173a1d521bdd8966df06a96afdf8f
Author: ambross.tison <<EMAIL>>
Date:   Sun Jun 22 08:34:31 2025 +0000

    make sample data and configuration for use of sample data

commit ff19678586d1a4a13ea17f4ed00dffdae2f5d11a
Author: ambross.tison <<EMAIL>>
Date:   Fri Jun 20 10:59:11 2025 +0000

    add stats endpoint

commit 9c411e37bd256d0e44cd7ad042892358fd907ff8
Author: ambross.tison <<EMAIL>>
Date:   Fri Jun 20 04:51:49 2025 +0000

    add ticket stats

commit 03092838229e8117893df17a8b7a6a8f782d23dd
Author: ambross.tison <<EMAIL>>
Date:   Wed Jun 11 04:57:05 2025 +0000

    fix handling of start_date end_date params

commit 5e40c7fe4101001f7f9eef42eccc446e8fb0131e
Author: root <<EMAIL>>
Date:   Tue Jun 10 11:20:39 2025 +0000

    sort by latest last_modified console logs

commit 0bf9e281975c19060545c542683c957efc8e7700
Author: root <<EMAIL>>
Date:   Tue Jun 10 10:41:21 2025 +0000

    sort by latest last_modified console log

commit 00a5f9ae9094619490be2fe1b8badc8a2e5ab9cd
Author: root <<EMAIL>>
Date:   Tue Jun 10 10:27:30 2025 +0000

    sort by latest last_modified console log

commit 8046a00ce554b04fb7e85a5a2af690de31bcea05
Author: root <<EMAIL>>
Date:   Tue Jun 10 10:02:37 2025 +0000

    sort by latest last_modified

commit 4e5c7920f3b41138eac777544f5f3f5e42a9be72
Author: ambross <<EMAIL>>
Date:   Fri May 23 11:17:03 2025 +0000

    change env and added static folder

commit 426f27a07e562afcf69b07f923370ee5f9a997f0
Merge: 7fe52f6 b795354
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Tue May 20 10:19:05 2025 +0000

    Merge branch 'ambross' into 'main'
    
    Ambross
    
    See merge request app-dev/glpi/glpi-api!4

commit b795354787c88e9b493372e56d6c7d4559594c79
Author: Ambross Balolong Tison <<EMAIL>>
Date:   Tue May 20 10:19:04 2025 +0000

    Ambross

commit 7fe52f6de31aab9cec0c1c3e937dc15307e5afb6
Merge: d79e121 19c443c
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Fri Apr 18 11:21:55 2025 +0000

    Merge branch 'ambross' into 'main'
    
    Ambross
    
    Closes #1
    
    See merge request app-dev/glpi/glpi-api!3

commit 19c443cc690f92597bc89755057425ce72c2718b
Author: root <<EMAIL>>
Date:   Sat Apr 12 05:05:01 2025 +0000

    fix ticket router to allow multiple user id param

commit 93f8de668d7fa94710f21cdd1d245746e649ee0c
Author: root <<EMAIL>>
Date:   Fri Apr 11 04:37:42 2025 +0000

    revert itilcategory router to orig

commit 614e9b883b3ae3f519c3ebbfc89d76812f3cb130
Author: root <<EMAIL>>
Date:   Fri Apr 11 04:33:42 2025 +0000

    fix user param to accept multiple

commit 8fdf227e84931ca6de3095c66822581c5709f569
Author: root <<EMAIL>>
Date:   Fri Apr 11 04:32:28 2025 +0000

    fix user param to accept multiple

commit 6656efbe404678bf0ac76653e03b409c3063f1ec
Author: root <<EMAIL>>
Date:   Wed Apr 9 04:49:37 2025 +0000

    add status parameter as list of ints

commit 38ec2cf17376bde7aad2f8cb1bd92414faca7be6
Author: root <<EMAIL>>
Date:   Tue Apr 8 23:22:37 2025 +0000

    fix error 500, fixes #1

commit ea6dc71b67023fea005a99c193ba6cb411b73169
Author: root <<EMAIL>>
Date:   Tue Apr 8 03:45:23 2025 +0000

    convert itil_category to pass multiple params

commit d79e121a3adcab9c05edb59a05c4ae9f50bc8367
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Mon Jan 27 20:29:02 2025 +0800

    add app dar settings endpoint; resolve issue with next monday getting included in the saved dar entries

commit 5f9bad3dca384a842a4f3afbe893e50e908b521b
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Tue Dec 17 12:45:29 2024 +0800

    add source_type and source_id for saving dar entries

commit f4d57a12b4b06f2fd08e238f3c6be7e8e6312b8b
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Mon Dec 16 12:26:45 2024 +0800

    Added source_type and source_id to reports/getdarentries endpoint response schema

commit f59aa6f1670861f29cbaf3564239e8d9cdd3f4d4
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Mon Dec 16 10:12:43 2024 +0800

    add "source_type" and "source_id" columns for dar_entry table -- use alembic for upgrade

commit 5968a123c3da2057efe7d6f0db1a14eee26d1e4c
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Sun Dec 15 12:10:00 2024 +0800

    add "is_deleted" filter for tickets

commit bdcb62917b26c081fe44b9025f98f2a2f033fe0f
Author: teo <<EMAIL>>
Date:   Wed Nov 6 17:43:03 2024 +0800

    fix error when date or duration fields are empty for tickettasks

commit a733caffc52da26b86f50d76619ac67d61ad3b9f
Author: teo <<EMAIL>>
Date:   Sun Oct 27 18:00:06 2024 +0800

    fixed additional_field_to_dt erroring out when date is blank in reports/getdarentries

commit 6f1e938ba2018c2e7c0ff26d27fb8b58af009cf3
Author: teo <<EMAIL>>
Date:   Tue Sep 17 18:27:35 2024 +0800

    initial
commit 3dbb770044470d1ee201429ac050dc556f06544e
Author: ambross.tison <<EMAIL>>
Date:   Sun Jul 6 08:10:14 2025 +0000

    add timeframe params to tickets by age and unit test

commit 7a52008ae03f6154364d6b23895beedfb39c8e38
Author: ambross.tison <<EMAIL>>
Date:   Sat Jul 5 10:37:11 2025 +0000

    add timeframe param for inactive ticket details

commit 34ca7be6433d851225e0e8ce23f192388a533e08
Author: ambross.tison <<EMAIL>>
Date:   Sat Jul 5 09:01:36 2025 +0000

    confirm closed tickets being returned

commit cd798c884c9dc7ebfb3a77287a93bd4e86b22f68
Author: ambross.tison <<EMAIL>>
Date:   Sat Jul 5 07:55:51 2025 +0000

    test inactive ticket endpoints

commit e1a344580451a487cefc31903d214e31f58d5de7
Author: ambross.tison <<EMAIL>>
Date:   Sat Jul 5 07:39:46 2025 +0000

    create new endpoint for inactive tickets

commit 4f38287d767f73512a54da590bdad80238bb953d
Author: ambross.tison <<EMAIL>>
Date:   Wed Jul 2 01:47:58 2025 +0000

    log the outputs

commit dd88a6de6d0fe4395e3a0bcedd893a4089b1293d
Author: ambross.tison <<EMAIL>>
Date:   Tue Jul 1 11:24:25 2025 +0000

    add ticket timeframe endpoint tests

commit 26a2e52ebd70e5fd2045b586c2fe4a70f94bb7d8
Author: ambross.tison <<EMAIL>>
Date:   Tue Jul 1 11:13:06 2025 +0000

    add ticket timeframe endpoints

commit 2d64a96c58fcb99ac3b23992048246fa5ee18c2d
Author: ambross.tison <<EMAIL>>
Date:   Sun Jun 29 04:20:16 2025 +0000

    write pytests for stats endpoint

commit b898db7e7c8ac021defc46f894c9cf1f450bfd70
Author: ambross.tison <<EMAIL>>
Date:   Tue Jun 24 10:10:35 2025 +0000

    add endpoint for no group tickets and write test

commit 22e24ce3b41b30af6a975af31848cb59f6481a07
Author: ambross.tison <<EMAIL>>
Date:   Tue Jun 24 04:08:43 2025 +0000

    add ticket stats unit test

commit 6ea7c4715fe3e46435ee0bcd8608d86c61be5e7b
Author: ambross.tison <<EMAIL>>
Date:   Sun Jun 22 09:14:31 2025 +0000

    fix TypeError: can't compare offset-naive and offset-aware datetimes

commit e75e69bebac173a1d521bdd8966df06a96afdf8f
Author: ambross.tison <<EMAIL>>
Date:   Sun Jun 22 08:34:31 2025 +0000

    make sample data and configuration for use of sample data

commit ff19678586d1a4a13ea17f4ed00dffdae2f5d11a
Author: ambross.tison <<EMAIL>>
Date:   Fri Jun 20 10:59:11 2025 +0000

    add stats endpoint

commit 9c411e37bd256d0e44cd7ad042892358fd907ff8
Author: ambross.tison <<EMAIL>>
Date:   Fri Jun 20 04:51:49 2025 +0000

    add ticket stats

commit 03092838229e8117893df17a8b7a6a8f782d23dd
Author: ambross.tison <<EMAIL>>
Date:   Wed Jun 11 04:57:05 2025 +0000

    fix handling of start_date end_date params

commit 5e40c7fe4101001f7f9eef42eccc446e8fb0131e
Author: root <<EMAIL>>
Date:   Tue Jun 10 11:20:39 2025 +0000

    sort by latest last_modified console logs

commit 0bf9e281975c19060545c542683c957efc8e7700
Author: root <<EMAIL>>
Date:   Tue Jun 10 10:41:21 2025 +0000

    sort by latest last_modified console log

commit 00a5f9ae9094619490be2fe1b8badc8a2e5ab9cd
Author: root <<EMAIL>>
Date:   Tue Jun 10 10:27:30 2025 +0000

    sort by latest last_modified console log

commit 8046a00ce554b04fb7e85a5a2af690de31bcea05
Author: root <<EMAIL>>
Date:   Tue Jun 10 10:02:37 2025 +0000

    sort by latest last_modified

commit 4e5c7920f3b41138eac777544f5f3f5e42a9be72
Author: ambross <<EMAIL>>
Date:   Fri May 23 11:17:03 2025 +0000

    change env and added static folder

commit 426f27a07e562afcf69b07f923370ee5f9a997f0
Merge: 7fe52f6 b795354
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Tue May 20 10:19:05 2025 +0000

    Merge branch 'ambross' into 'main'
    
    Ambross
    
    See merge request app-dev/glpi/glpi-api!4

commit b795354787c88e9b493372e56d6c7d4559594c79
Author: Ambross Balolong Tison <<EMAIL>>
Date:   Tue May 20 10:19:04 2025 +0000

    Ambross

commit 7fe52f6de31aab9cec0c1c3e937dc15307e5afb6
Merge: d79e121 19c443c
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Fri Apr 18 11:21:55 2025 +0000

    Merge branch 'ambross' into 'main'
    
    Ambross
    
    Closes #1
    
    See merge request app-dev/glpi/glpi-api!3

commit 19c443cc690f92597bc89755057425ce72c2718b
Author: root <<EMAIL>>
Date:   Sat Apr 12 05:05:01 2025 +0000

    fix ticket router to allow multiple user id param

commit 93f8de668d7fa94710f21cdd1d245746e649ee0c
Author: root <<EMAIL>>
Date:   Fri Apr 11 04:37:42 2025 +0000

    revert itilcategory router to orig

commit 614e9b883b3ae3f519c3ebbfc89d76812f3cb130
Author: root <<EMAIL>>
Date:   Fri Apr 11 04:33:42 2025 +0000

    fix user param to accept multiple

commit 8fdf227e84931ca6de3095c66822581c5709f569
Author: root <<EMAIL>>
Date:   Fri Apr 11 04:32:28 2025 +0000

    fix user param to accept multiple

commit 6656efbe404678bf0ac76653e03b409c3063f1ec
Author: root <<EMAIL>>
Date:   Wed Apr 9 04:49:37 2025 +0000

    add status parameter as list of ints

commit 38ec2cf17376bde7aad2f8cb1bd92414faca7be6
Author: root <<EMAIL>>
Date:   Tue Apr 8 23:22:37 2025 +0000

    fix error 500, fixes #1

commit ea6dc71b67023fea005a99c193ba6cb411b73169
Author: root <<EMAIL>>
Date:   Tue Apr 8 03:45:23 2025 +0000

    convert itil_category to pass multiple params

commit d79e121a3adcab9c05edb59a05c4ae9f50bc8367
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Mon Jan 27 20:29:02 2025 +0800

    add app dar settings endpoint; resolve issue with next monday getting included in the saved dar entries

commit 5f9bad3dca384a842a4f3afbe893e50e908b521b
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Tue Dec 17 12:45:29 2024 +0800

    add source_type and source_id for saving dar entries

commit f4d57a12b4b06f2fd08e238f3c6be7e8e6312b8b
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Mon Dec 16 12:26:45 2024 +0800

    Added source_type and source_id to reports/getdarentries endpoint response schema

commit f59aa6f1670861f29cbaf3564239e8d9cdd3f4d4
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Mon Dec 16 10:12:43 2024 +0800

    add "source_type" and "source_id" columns for dar_entry table -- use alembic for upgrade

commit 5968a123c3da2057efe7d6f0db1a14eee26d1e4c
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Sun Dec 15 12:10:00 2024 +0800

    add "is_deleted" filter for tickets

commit bdcb62917b26c081fe44b9025f98f2a2f033fe0f
Author: teo <<EMAIL>>
Date:   Wed Nov 6 17:43:03 2024 +0800

    fix error when date or duration fields are empty for tickettasks

commit a733caffc52da26b86f50d76619ac67d61ad3b9f
Author: teo <<EMAIL>>
Date:   Sun Oct 27 18:00:06 2024 +0800

    fixed additional_field_to_dt erroring out when date is blank in reports/getdarentries

commit 6f1e938ba2018c2e7c0ff26d27fb8b58af009cf3
Author: teo <<EMAIL>>
Date:   Tue Sep 17 18:27:35 2024 +0800

    initial
commit 3dbb770044470d1ee201429ac050dc556f06544e
Author: ambross.tison <<EMAIL>>
Date:   Sun Jul 6 08:10:14 2025 +0000

    add timeframe params to tickets by age and unit test

commit 7a52008ae03f6154364d6b23895beedfb39c8e38
Author: ambross.tison <<EMAIL>>
Date:   Sat Jul 5 10:37:11 2025 +0000

    add timeframe param for inactive ticket details

commit 34ca7be6433d851225e0e8ce23f192388a533e08
Author: ambross.tison <<EMAIL>>
Date:   Sat Jul 5 09:01:36 2025 +0000

    confirm closed tickets being returned

commit cd798c884c9dc7ebfb3a77287a93bd4e86b22f68
Author: ambross.tison <<EMAIL>>
Date:   Sat Jul 5 07:55:51 2025 +0000

    test inactive ticket endpoints

commit e1a344580451a487cefc31903d214e31f58d5de7
Author: ambross.tison <<EMAIL>>
Date:   Sat Jul 5 07:39:46 2025 +0000

    create new endpoint for inactive tickets

commit 4f38287d767f73512a54da590bdad80238bb953d
Author: ambross.tison <<EMAIL>>
Date:   Wed Jul 2 01:47:58 2025 +0000

    log the outputs

commit dd88a6de6d0fe4395e3a0bcedd893a4089b1293d
Author: ambross.tison <<EMAIL>>
Date:   Tue Jul 1 11:24:25 2025 +0000

    add ticket timeframe endpoint tests

commit 26a2e52ebd70e5fd2045b586c2fe4a70f94bb7d8
Author: ambross.tison <<EMAIL>>
Date:   Tue Jul 1 11:13:06 2025 +0000

    add ticket timeframe endpoints

commit 2d64a96c58fcb99ac3b23992048246fa5ee18c2d
Author: ambross.tison <<EMAIL>>
Date:   Sun Jun 29 04:20:16 2025 +0000

    write pytests for stats endpoint

commit b898db7e7c8ac021defc46f894c9cf1f450bfd70
Author: ambross.tison <<EMAIL>>
Date:   Tue Jun 24 10:10:35 2025 +0000

    add endpoint for no group tickets and write test

commit 22e24ce3b41b30af6a975af31848cb59f6481a07
Author: ambross.tison <<EMAIL>>
Date:   Tue Jun 24 04:08:43 2025 +0000

    add ticket stats unit test

commit 6ea7c4715fe3e46435ee0bcd8608d86c61be5e7b
Author: ambross.tison <<EMAIL>>
Date:   Sun Jun 22 09:14:31 2025 +0000

    fix TypeError: can't compare offset-naive and offset-aware datetimes

commit e75e69bebac173a1d521bdd8966df06a96afdf8f
Author: ambross.tison <<EMAIL>>
Date:   Sun Jun 22 08:34:31 2025 +0000

    make sample data and configuration for use of sample data

commit ff19678586d1a4a13ea17f4ed00dffdae2f5d11a
Author: ambross.tison <<EMAIL>>
Date:   Fri Jun 20 10:59:11 2025 +0000

    add stats endpoint

commit 9c411e37bd256d0e44cd7ad042892358fd907ff8
Author: ambross.tison <<EMAIL>>
Date:   Fri Jun 20 04:51:49 2025 +0000

    add ticket stats

commit 03092838229e8117893df17a8b7a6a8f782d23dd
Author: ambross.tison <<EMAIL>>
Date:   Wed Jun 11 04:57:05 2025 +0000

    fix handling of start_date end_date params

commit 5e40c7fe4101001f7f9eef42eccc446e8fb0131e
Author: root <<EMAIL>>
Date:   Tue Jun 10 11:20:39 2025 +0000

    sort by latest last_modified console logs

commit 0bf9e281975c19060545c542683c957efc8e7700
Author: root <<EMAIL>>
Date:   Tue Jun 10 10:41:21 2025 +0000

    sort by latest last_modified console log

commit 00a5f9ae9094619490be2fe1b8badc8a2e5ab9cd
Author: root <<EMAIL>>
Date:   Tue Jun 10 10:27:30 2025 +0000

    sort by latest last_modified console log

commit 8046a00ce554b04fb7e85a5a2af690de31bcea05
Author: root <<EMAIL>>
Date:   Tue Jun 10 10:02:37 2025 +0000

    sort by latest last_modified

commit 4e5c7920f3b41138eac777544f5f3f5e42a9be72
Author: ambross <<EMAIL>>
Date:   Fri May 23 11:17:03 2025 +0000

    change env and added static folder

commit 426f27a07e562afcf69b07f923370ee5f9a997f0
Merge: 7fe52f6 b795354
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Tue May 20 10:19:05 2025 +0000

    Merge branch 'ambross' into 'main'
    
    Ambross
    
    See merge request app-dev/glpi/glpi-api!4

commit b795354787c88e9b493372e56d6c7d4559594c79
Author: Ambross Balolong Tison <<EMAIL>>
Date:   Tue May 20 10:19:04 2025 +0000

    Ambross

commit 7fe52f6de31aab9cec0c1c3e937dc15307e5afb6
Merge: d79e121 19c443c
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Fri Apr 18 11:21:55 2025 +0000

    Merge branch 'ambross' into 'main'
    
    Ambross
    
    Closes #1
    
    See merge request app-dev/glpi/glpi-api!3

commit 19c443cc690f92597bc89755057425ce72c2718b
Author: root <<EMAIL>>
Date:   Sat Apr 12 05:05:01 2025 +0000

    fix ticket router to allow multiple user id param

commit 93f8de668d7fa94710f21cdd1d245746e649ee0c
Author: root <<EMAIL>>
Date:   Fri Apr 11 04:37:42 2025 +0000

    revert itilcategory router to orig

commit 614e9b883b3ae3f519c3ebbfc89d76812f3cb130
Author: root <<EMAIL>>
Date:   Fri Apr 11 04:33:42 2025 +0000

    fix user param to accept multiple

commit 8fdf227e84931ca6de3095c66822581c5709f569
Author: root <<EMAIL>>
Date:   Fri Apr 11 04:32:28 2025 +0000

    fix user param to accept multiple

commit 6656efbe404678bf0ac76653e03b409c3063f1ec
Author: root <<EMAIL>>
Date:   Wed Apr 9 04:49:37 2025 +0000

    add status parameter as list of ints

commit 38ec2cf17376bde7aad2f8cb1bd92414faca7be6
Author: root <<EMAIL>>
Date:   Tue Apr 8 23:22:37 2025 +0000

    fix error 500, fixes #1

commit ea6dc71b67023fea005a99c193ba6cb411b73169
Author: root <<EMAIL>>
Date:   Tue Apr 8 03:45:23 2025 +0000

    convert itil_category to pass multiple params

commit d79e121a3adcab9c05edb59a05c4ae9f50bc8367
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Mon Jan 27 20:29:02 2025 +0800

    add app dar settings endpoint; resolve issue with next monday getting included in the saved dar entries

commit 5f9bad3dca384a842a4f3afbe893e50e908b521b
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Tue Dec 17 12:45:29 2024 +0800

    add source_type and source_id for saving dar entries

commit f4d57a12b4b06f2fd08e238f3c6be7e8e6312b8b
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Mon Dec 16 12:26:45 2024 +0800

    Added source_type and source_id to reports/getdarentries endpoint response schema

commit f59aa6f1670861f29cbaf3564239e8d9cdd3f4d4
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Mon Dec 16 10:12:43 2024 +0800

    add "source_type" and "source_id" columns for dar_entry table -- use alembic for upgrade

commit 5968a123c3da2057efe7d6f0db1a14eee26d1e4c
Author: Teodorico Ramos Jr. <<EMAIL>>
Date:   Sun Dec 15 12:10:00 2024 +0800

    add "is_deleted" filter for tickets

commit bdcb62917b26c081fe44b9025f98f2a2f033fe0f
Author: teo <<EMAIL>>
Date:   Wed Nov 6 17:43:03 2024 +0800

    fix error when date or duration fields are empty for tickettasks

commit a733caffc52da26b86f50d76619ac67d61ad3b9f
Author: teo <<EMAIL>>
Date:   Sun Oct 27 18:00:06 2024 +0800

    fixed additional_field_to_dt erroring out when date is blank in reports/getdarentries

commit 6f1e938ba2018c2e7c0ff26d27fb8b58af009cf3
Author: teo <<EMAIL>>
Date:   Tue Sep 17 18:27:35 2024 +0800

    initial
