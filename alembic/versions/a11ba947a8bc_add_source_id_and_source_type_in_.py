"""add source_id and source_type in DarEntry

Revision ID: a11ba947a8bc
Revises: 
Create Date: 2024-12-15 16:49:16.668647

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'a11ba947a8bc'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('dar_entry', sa.Column('source_type', mysql.VARCHAR(length=255), nullable=True))
    op.add_column('dar_entry', sa.Column('source_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))


def downgrade() -> None:
    op.drop_column('dar_entry', 'source_type')
    op.drop_column('dar_entry', 'source_id')
