"""add_parent_ticket_id_and_parent_ticket_title_in_dar_entry

Revision ID: 991a5b1843f8
Revises: a11ba947a8bc
Create Date: 2025-08-02 06:01:06.975827

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '991a5b1843f8'
down_revision: Union[str, None] = 'a11ba947a8bc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('dar_entry', sa.Column('parent_ticket_id', sa.Integer(), nullable=True))
    op.add_column('dar_entry', sa.Column('parent_ticket_title', sa.String(length=255), nullable=True))


def downgrade() -> None:
    op.drop_column('dar_entry', 'parent_ticket_title')
    op.drop_column('dar_entry', 'parent_ticket_id')
