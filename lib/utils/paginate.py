from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate as sqlalchemy_paginate
from typing import TypeVar, Generic, Sequence, List, Optional, Union, Any, Type, Dict

T = TypeVar('T')

def paginate(query, params=None):
    """
    Wrapper around fastapi_pagination.ext.sqlalchemy.paginate
    
    Args:
        query: SQLAlchemy query object
        params: Pagination parameters
        
    Returns:
        Paginated result
    """
    return sqlalchemy_paginate(query, params)


class Params:
    """
    Pagination parameters class
    """
    def __init__(self, page: int = 1, size: int = 50):
        self.page = page
        self.size = size