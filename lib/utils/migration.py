from alembic import command
from alembic.config import Config
import os
import logging

logger = logging.getLogger(__name__)

def run_migrations():
    """
    Run database migrations automatically using Alembic.
    This function can be called during application startup to ensure
    the database schema is up to date.
    """
    try:
        # Get the directory of the current file
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        # Create an Alembic configuration object
        alembic_cfg = Config(os.path.join(current_dir, 'alembic.ini'))
        
        # Set the script location
        alembic_cfg.set_main_option('script_location', os.path.join(current_dir, 'alembic'))
        
        # Run the migration
        logger.info("Running database migrations...")
        command.upgrade(alembic_cfg, 'head')
        logger.info("Database migrations completed successfully.")
        return True
    except Exception as e:
        logger.error(f"Error running database migrations: {str(e)}")
        return False