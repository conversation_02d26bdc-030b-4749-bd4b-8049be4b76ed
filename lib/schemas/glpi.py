import datetime
from pydantic import BaseModel, ConfigD<PERSON>, <PERSON>rict, RootModel, model_validator
from typing import Optional, Annotated, List
from datetime import datetime, date, timedelta
from fastapi_pagination import Page

class Entity(BaseModel):
    id: int
    name: str
    complete_name: str
    comment: Optional[str] = None

    class Config:
        from_attributes = True


class User(BaseModel):
    id: int
    name: str
    first_name: Optional[str]
    last_name: Optional[str]

    class Config:
        from_attributes = True


class GroupUser(BaseModel):
    id: int
    user: User
    group_id: int

    class Config:
        from_attributes = True


class Group(BaseModel):
    id: int
    entity_id: int
    name: str
    complete_name: str

    group_users: List[GroupUser]

    class Config:
        from_attributes = True


class TicketUser(BaseModel):
    id: int
    user: User
    type: int
    ticket_id: int

    class Config:
        from_attributes = True


class ItilCategory(BaseModel):
    id: int
    # entity: Entity
    is_recursive: int

    name: str
    complete_name: str
    comment: Optional[str]

    class Config:
        from_attributes = True


class TicketAdditionalField(BaseModel):
    id: int
    vem_num: Optional[str]
    aevm_num: Optional[str]
    atg_num: Optional[str]
    start_time: Optional[str]
    end_time: Optional[str]

    class Config:
        from_attributes = True    


class Ticket(BaseModel):

    id: int
    entity: Entity

    name: str
    date: datetime    
    date_modified: datetime
    open_date: Optional[datetime] = None
    close_date: Optional[datetime] = None
    solve_date: Optional[datetime] = None
    status: int
    content: str
    urgency: int
    impact: int
    priority: int
    is_deleted: int

    additional_field: Optional[TicketAdditionalField]

    ticket_users: List[TicketUser]
    itil_category: Optional[ItilCategory]
    duration: Optional[float]
    observer_groups: List[Group] = []

    class Config:
        from_attributes = True


class TicketTask(BaseModel):
    id: int 
    ticket_id: int
    ticket_name: str
    
    content: str
    date: Optional[datetime] = None
    start_date: Optional[str] = None
    duration: Optional[int]
    duration_hr: Optional[float]

    short_description: Optional[str] = None
    additional_details: Optional[str] = None

    user: User
    technician_id: int
    group_id: int

    class Config:
        from_attributes = True
        orm_mode = True   


class TicketWithTicketTasks(BaseModel):

    id: int
    entity: Entity

    name: str
    date: datetime    
    date_modified: datetime
    open_date: Optional[datetime] = None
    close_date: Optional[datetime] = None
    solve_date: Optional[datetime] = None
    status: int
    content: str
    urgency: int
    impact: int
    priority: int
    itil_category: Optional[ItilCategory]
    duration: Optional[float]

    additional_field: Optional[TicketAdditionalField]
    ticket_users: List[TicketUser]
    ticket_tasks: List[TicketTask]

    class Config:
        from_attributes = True


class GroupTicket(BaseModel):
    id: int
    group_id: int
    ticket_id: int
    type: int

    class Config:
        from_attributes = True


class ItilFollowups(BaseModel):
    id: int
    content: str

    start_date: Optional[str] = None
    end_date: Optional[str] = None
    update: Optional[str] = None

    class Config:
        from_attributes = True    


class Settings(BaseModel):
    first_monday: date

    class Config:
        from_attributes = True   


class Token(BaseModel):
    access_token: str
    token_type: str

class FormAnswer(BaseModel):
    id: int
    name: Optional[str] = None
    forms_id: int
    entities_id: int
    requester_id: int
    request_date: Optional[datetime] = None
    groups_id_validator: int
    form_category_id: Optional[int] = None
    form_category_name: Optional[str] = None  # Added field for completename
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    duration: Optional[float] = None

    @model_validator(mode='after')
    def compute_duration(self) -> 'FormAnswer':
        start_time_str = self.start_time
        end_time_str = self.end_time
        if start_time_str and end_time_str:
            try:
                from datetime import datetime
                start_time = datetime.strptime(start_time_str, '%Y-%m-%d %H:%M:%S')
                end_time = datetime.strptime(end_time_str, '%Y-%m-%d %H:%M:%S')
                self.duration = (end_time - start_time).total_seconds() / 3600
            except ValueError:
                self.duration = None
        return self

    class Config:
        from_attributes = True
        populate_by_name = True
        alias_generator = lambda field_name: {
            "forms_id": "plugin_formcreator_forms_id",
            "start_time": "start_time",
            "end_time": "end_time",
            "form_category_id": "form_category_id"
        }.get(field_name, field_name)


class GroupTicketCount(BaseModel):
    group: Group
    ticket_count: int

    class Config:
        from_attributes = True


class TicketTimeframeCounts(BaseModel):
    today: int
    past_7_days: int # Last 7 days, excluding today
    past_8_to_14_days: int # 8 to 14 days ago
    past_15_to_30_days: int # 15 to 30 days ago
    past_31_to_60_days: int # 31 to 60 days ago
    past_61_to_90_days: int # 61 to 90 days ago
    past_91_days_to_6_months: int # 91 days to 6 months ago
    past_6_months_to_1_year: int # 6 months to 1 year ago
    past_1_to_2_years: int # 1 year to 2 years ago
    older_than_2_years: int # More than 2 years ago


class NoGroupOverallTicketStats(BaseModel):
    count: int

    class Config:
        from_attributes = True


class NoGroupTicketsResponse(BaseModel):
    overall_stats: NoGroupOverallTicketStats
    tickets: Page[Ticket]

    class Config:
        from_attributes = True


class GroupTicketTimeframeCount(BaseModel):
    group: Group
    counts: TicketTimeframeCounts

    class Config:
        from_attributes = True


class TicketTimeframeDetails(BaseModel):
    today_tickets: List[Ticket]
    past_7_days_tickets: List[Ticket]
    past_8_to_14_days_tickets: List[Ticket]
    past_15_to_30_days_tickets: List[Ticket]
    past_31_to_60_days_tickets: List[Ticket]
    past_61_to_90_days_tickets: List[Ticket]
    past_91_days_to_6_months_tickets: List[Ticket]
    past_6_months_to_1_year_tickets: List[Ticket]
    past_1_to_2_years_tickets: List[Ticket]
    older_than_2_years_tickets: List[Ticket]


class GroupTicketTimeframeDetails(BaseModel):
    group: Group
    details: TicketTimeframeDetails

    class Config:
        from_attributes = True
