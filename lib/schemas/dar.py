import datetime
from pydantic import BaseModel, ConfigDict, Strict, RootModel
from typing import Optional, Annotated, List
from datetime import datetime, date


class GLPIDarEntry(BaseModel):
    start_dt: Optional[datetime]
    start_date: str
    start_time: str
    end_time: str
    task: Optional[str]
    source_type: Optional[str]
    source_id: Optional[int]
    parent_ticket_id: Optional[int]
    parent_ticket_title: Optional[str]

    class Config:
        from_attributes = True


class GLPIDarEntries(BaseModel):
    entries: List[GLPIDarEntry]

    class Config:
        from_attributes = True


class DarEntryCreateInput(BaseModel):
    user_id: int
    week: int
    start_date: str
    start_time: str
    end_time: str
    task: str
    source_type: Optional[str]
    source_id: Optional[int]
    parent_ticket_id: Optional[int]
    parent_ticket_title: Optional[str]

    class Config:
        from_attributes = True


class DarEntryCreateInputList(BaseModel):
    entries: List[DarEntryCreateInput]

    class Config:
        from_attributes = True


class DarEntryList(BaseModel):
    ids: List[int]


class DarEntry(BaseModel):
    id: int
    start_date: str
    start_time: str
    end_time: str
    task: str
    source_type: Optional[str]
    source_id: Optional[int]
    parent_ticket_id: Optional[int]
    parent_ticket_title: Optional[str]    


class DarEntryWithDetails(BaseModel):
    ids: List[int]
    entries: List[DarEntry]


class DarSetting(BaseModel):
    id: int
    name: str
    subname: str
    value: str


class DarSettingCreate(BaseModel):
    name: str
    subname: str
    value: str


class DarSettingsList(BaseModel):
    settings: List[DarSetting]


class DarSettingsCreateList(BaseModel):
    settings: List[DarSettingCreate]