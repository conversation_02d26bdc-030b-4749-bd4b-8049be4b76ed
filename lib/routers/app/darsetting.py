from datetime import datetime
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Query, Depends, APIRouter
from sqlalchemy import delete
from sqlalchemy.orm import Session

from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate

from database import get_app_db
import lib.schemas.dar as sc
import lib.models.dar as mdl
from lib.auth import get_current_user


TAG = 'APP DAR Setting'
router = APIRouter()


@router.get('/', 
            tags=[TAG], 
            response_model=sc.DarSettingsList, 
            status_code=200)
def get_dar_settings(user: dict = Depends(get_current_user),
                     db: Session = Depends(get_app_db)):
    result = db.query(mdl.DarSetting).all()
    return {
        "settings": result
    }


@router.post('/replace',
             tags=[TAG], 
             response_model=sc.DarSettingsList,
             status_code=201)
def replace_dar_settings(dar_settings: sc.DarSettingsCreateList, 
                         user: dict = Depends(get_current_user),
                         db: Session = Depends(get_app_db)):
    stmt = delete(mdl.DarSetting)
    db.execute(stmt)
    db.commit()

    for setting in dar_settings.settings:
        db_obj = mdl.DarSetting(name=setting.name,
                                subname=setting.subname,
                                value=setting.value)
        db.add(db_obj)
        db.flush()
    db.commit()

    result = db.query(mdl.DarSetting).all()
    return {
        "settings": result
    }
    