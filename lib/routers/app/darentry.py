from datetime import datetime
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Query, Depends, APIRouter
from sqlalchemy import delete
from sqlalchemy.orm import Session

from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate

from database import get_app_db
import lib.schemas.dar as sc
import lib.models.dar as mdl
from lib.auth import get_current_user


TAG = 'APP DAR Entry'
router = APIRouter()


@router.get('/{id}', tags=[TAG], response_model=sc.DarEntry, status_code=200)
def get_dar_entry_by_id(id: int, 
                        user: dict = Depends(get_current_user),
                        db: Session = Depends(get_app_db)):
    q = db.query(mdl.DarEntry).filter(mdl.DarEntry.id == id).first()
    if q is None:
        raise HTTPException(
            status_code=404, detail=f'No DAR entry found.'
        )
    return q


@router.get('/', 
            tags=[TAG], 
            response_model=sc.Dar<PERSON>ntryCreateInputList, 
            status_code=200)
def get_dar_entry(user_id: int = None, 
                  start_date: datetime = None,
                  end_date: datetime = None,
                  user: dict = Depends(get_current_user),
                  db: Session = Depends(get_app_db)):
    result = db.query(mdl.DarEntry).filter(mdl.DarEntry.user_id == user_id)
    if start_date is not None and end_date is not None:
        result = result.filter(mdl.DarEntry.start_dt.between(start_date, end_date))
    return {'entries': result}


@router.post('/',
             tags=[TAG], 
             response_model=sc.DarEntry, 
             status_code=201)
def create_dar_entry(dar_entry: sc.DarEntryCreateInput, 
                     user: dict = Depends(get_current_user),
                     db: Session = Depends(get_app_db)):
    dt_str = f'{dar_entry.start_date} {dar_entry.start_time}'
    dt = datetime.strptime(dt_str, "%B %d, %Y %I:%M %p")
    db_obj = mdl.DarEntry(user_id=dar_entry.user_id,
                          week=dar_entry.week,
                          start_dt=dt,
                          start_date=dar_entry.start_date,
                          start_time=dar_entry.start_time,
                          end_time=dar_entry.end_time,
                          task=dar_entry.task,
                          source_id=dar_entry.source_id,
                          source_type=dar_entry.source_type,
                          parent_ticket_id=dar_entry.parent_ticket_id,
                          parent_ticket_title=dar_entry.parent_ticket_title)
    db.add(db_obj)
    db.commit()
    return db_obj


@router.post('/list',
             tags=[TAG], 
             response_model=sc.DarEntryWithDetails, 
             status_code=201)
def create_dar_entry(dar_entry: sc.DarEntryCreateInputList, 
                     user: dict = Depends(get_current_user),
                     db: Session = Depends(get_app_db)):
    ids = []
    created_entries = []
    for entry in dar_entry.entries:
        dt = datetime.strptime(f'{entry.start_date} {entry.start_time}', "%B %d, %Y %I:%M %p")
        db_obj = mdl.DarEntry(user_id=entry.user_id,
                              week=entry.week,
                              start_dt=dt,
                              start_date=entry.start_date,
                              start_time=entry.start_time,
                              end_time=entry.end_time,
                              task=entry.task,
                              source_id=entry.source_id,
                              source_type=entry.source_type,
                              parent_ticket_id=entry.parent_ticket_id,
                              parent_ticket_title=entry.parent_ticket_title)
        db.add(db_obj)
        db.flush()
        ids.append(db_obj.id)
        created_entries.append({
            'id': db_obj.id,
            'start_date': db_obj.start_date,
            'start_time': db_obj.start_time,
            'end_time': db_obj.end_time,
            'task': db_obj.task,
            'source_type': db_obj.source_type,
            'source_id': db_obj.source_id,
            'parent_ticket_id': db_obj.parent_ticket_id,
            'parent_ticket_title': db_obj.parent_ticket_title
        })
    db.commit()
    return {'ids': ids, 'entries': created_entries}


@router.delete('/list',
               tags=[TAG], 
               response_model=sc.DarEntryList, 
               status_code=201)
def delete_dar_entry(user_id: int, week: int, 
                     user: dict = Depends(get_current_user),
                     db: Session = Depends(get_app_db)):
    res = db.query(mdl.DarEntry).filter(mdl.DarEntry.user_id == user_id, mdl.DarEntry.week == week)
    ids = [q.id for q in res]
    db.execute(delete(mdl.DarEntry).where(mdl.DarEntry.user_id == user_id, mdl.DarEntry.week == week))
    db.commit()
    return {'ids': ids}

    