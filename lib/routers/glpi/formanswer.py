from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import Session
from fastapi import HTTPException, Query, Depends, APIRouter
from fastapi_pagination import Page, paginate
from fastapi_filter import FilterDepends
from typing import Optional
from datetime import datetime

from database import get_glpi_db
import lib.schemas.glpi as schemas
from lib.models.forms import FormAnswer, Form, FormCategory, Question, Answer
from lib.models.glpi import User
from lib.auth import get_current_user

TAG = 'GLPI Form Answer'

router = APIRouter()

@router.get('/', 
            tags=[TAG], 
            response_model=Page[schemas.FormAnswer], 
            status_code=200)
def get_form_answer(
    forms_id: Optional[int] = None,
    entities_id: Optional[int] = None,
    requester_id: Optional[int] = None,
    groups_id_validator: Optional[int] = None,
    form_category_id: Optional[int] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    user: dict = Depends(get_current_user),
    db: Session = Depends(get_glpi_db)
):
    # Subquery to get timestamps for question 1 and 2 for each FormAnswer
    answer_subquery = db.query(
        Answer.plugin_formcreator_formanswers_id,
        func.max(func.if_(
            or_(
                Question.name == 'Start Time',
                Question.name == 'start date'
            ), 
            Answer.answer, 
            None
        )).label('start_time'),
        func.max(func.if_(
            or_(
                Question.name == 'End Time',
                Question.name == 'end date'
            ), 
            Answer.answer, 
            None
        )).label('end_time'),
        func.max(func.if_(
            or_(
                Question.name == 'Team',
                Question.name.like('%Team%')
            ),
            Answer.answer,
            None
        )).label('groups_id_validator')
    ).join(
        Question,
        Answer.plugin_formcreator_questions_id == Question.id
    ).group_by(Answer.plugin_formcreator_formanswers_id).subquery()

    # Base query
    result = db.query(
        FormAnswer,
        answer_subquery.c.start_time,
        answer_subquery.c.end_time,
        answer_subquery.c.groups_id_validator,
        FormCategory.plugin_formcreator_categories_id,
        FormCategory.completename.label('form_category_name')
    ).outerjoin(
        answer_subquery,
        FormAnswer.id == answer_subquery.c.plugin_formcreator_formanswers_id
    ).outerjoin(
        Form,
        FormAnswer.plugin_formcreator_forms_id == Form.id
    ).outerjoin(
        FormCategory,
        Form.plugin_formcreator_categories_id == FormCategory.id
    )

    # Apply filters
    if forms_id:
        result = result.filter(FormAnswer.plugin_formcreator_forms_id == forms_id)
    if entities_id:
        result = result.filter(FormAnswer.entities_id == entities_id)
    if requester_id:
        result = result.filter(FormAnswer.requester_id == requester_id)
    if form_category_id:
        result = result.filter(FormCategory.id == form_category_id)
    if groups_id_validator:
        # Add filter for groups_id_validator
        result = result.filter(answer_subquery.c.groups_id_validator == str(groups_id_validator))  # Convert to string since it's stored as text in answers

    # Process results and apply attributes
    processed_results = []
    for form_answer, start_time, end_time, groups_id_validator, category_id, category_name in result.all():
        form_answer.start_time = start_time
        form_answer.end_time = end_time
        form_answer.groups_id_validator = int(groups_id_validator) if groups_id_validator else 0
        form_answer.form_category_id = category_id
        form_answer.form_category_name = category_name
        processed_results.append(form_answer)

    return paginate(processed_results)  # In-memory pagination
