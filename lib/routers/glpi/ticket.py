import os
import json
from pathlib import Path
from fastapi import HTTPException, Query, Depends, APIRouter, Response
from fastapi_pagination import Page, Params
from typing import List, Union
from datetime import datetime

from sqlalchemy import select, and_, any_, or_
from sqlalchemy.orm import Session
from fastapi_pagination.ext.sqlalchemy import paginate

from database import get_glpi_db
from lib.auth import get_current_user
import lib.schemas.glpi as schemas
import lib.models.glpi as model
import lib.crud.ticket as crud_ticket
import lib.filters as filters
import lib.crud.utils as crud_utils

TAG = 'GLPI Ticket'

router = APIRouter()

USE_SAMPLE_DATA = os.getenv("USE_SAMPLE_TICKET_DATA", "false").lower() == "true"
print(f"DEBUG: USE_SAMPLE_DATA is set to {USE_SAMPLE_DATA}")

SAMPLE_TICKETS = []
if USE_SAMPLE_DATA:
    # Define the path to the sample_tickets.json relative to this file
    SAMPLE_DATA_PATH = Path(__file__).parent.parent.parent.parent / "sample_tickets.json"
    print(f"DEBUG: Attempting to load sample data from {SAMPLE_DATA_PATH}")
    # Load sample data once when the module is loaded
    try:
        with open(SAMPLE_DATA_PATH, 'r') as f:
            SAMPLE_TICKETS = json.load(f)
        print(f"DEBUG: Successfully loaded {len(SAMPLE_TICKETS)} sample tickets.")
    except FileNotFoundError:
        print(f"Error: sample_tickets.json not found at {SAMPLE_DATA_PATH}")
        SAMPLE_TICKETS = [] # Handle case where file is not found

@router.get('/{id}', tags=[TAG], response_model=schemas.Ticket, status_code=200)
async def get_ticket_by_id(
    id: int,
    user: dict = Depends(get_current_user),
    db: Session = Depends(get_glpi_db)
):
    if USE_SAMPLE_DATA:
        for ticket in SAMPLE_TICKETS:
            if ticket["id"] == id:
                return ticket
        raise HTTPException(status_code=404, detail='No item found.')
    else:
        db_ent = crud_ticket.get_ticket_by_id(db, id=id)
        if db_ent is None:
            raise HTTPException(status_code=404, detail='No item found.')
        return db_ent


@router.get('/', tags=[TAG], response_model=Union[Page[schemas.Ticket], int], status_code=200)
async def get_ticket(
    user_id: List[int] = Query(default=[]),
    params: Params = Depends(),
    assignment_type: int = 2,
    ticket_name_contains: str = None,
    itil_category_id: List[int] = Query(default=[]),
    entity_id: int = None,
    group_id: int = None,
    status: List[int] = Query(default=[]),
    start_opening_date: datetime = None,
    end_opening_date: datetime = None,
    closed: bool = None,
    start_closing_date: datetime = None,
    end_closing_date: datetime = None,
    start_date: datetime = None,
    end_date: datetime = None,
    is_deleted: bool = False,
    count_only: bool = False,
    user: dict = Depends(get_current_user),
    db: Session = Depends(get_glpi_db)
):
    if USE_SAMPLE_DATA:
        # Start with all sample tickets
        filtered_tickets = SAMPLE_TICKETS

        # Apply filters
        if ticket_name_contains:
            filtered_tickets = [
                ticket for ticket in filtered_tickets
                if ticket_name_contains.lower() in ticket.get("name", "").lower()
            ]

        if itil_category_id:
            filtered_tickets = [
                ticket for ticket in filtered_tickets
                if ticket.get("itil_category_id") in itil_category_id
            ]

        if status:
            filtered_tickets = [
                ticket for ticket in filtered_tickets
                if ticket.get("status") in status
            ]

        # For now, let's just return the count or paginated items
        if count_only:
            return len(filtered_tickets)

        # Manual pagination
        total_items = len(filtered_tickets)
        start_index = (params.page - 1) * params.size
        end_index = start_index + params.size
        paginated_items = filtered_tickets[start_index:end_index]

        # Construct the Page object manually
        return Page[schemas.Ticket](
            items=paginated_items,
            total=total_items,
            page=params.page,
            size=params.size,
            pages=(total_items + params.size - 1) // params.size # Ceiling division
        )
    else:
        '''
        Assignment Types:
        1 - requester
        2 - assigned to
        3 - observer
        '''

        # Initial query setup
        if start_date and end_date:
            dates = crud_utils.generate_date_range(start_date, end_date)
            conditions = [model.TicketAdditionalField.start_time.like(f"%{one_date}%") for one_date in dates]
            ticket_ids_query = db.query(model.TicketAdditionalField.ticket_id).filter(or_(*conditions))
            subq = (
                db.query(model.TicketAdditionalField.ticket_id)
                .filter(or_(*conditions))
                .distinct()
                .subquery()
            )

            result = db.query(model.Ticket).filter(model.Ticket.id.in_(select(subq)))

        else:
            result = db.query(model.Ticket)
        # Opening date filter
        if start_opening_date and end_opening_date:
            result = result.filter(model.Ticket.open_date.between(start_opening_date, end_opening_date))
        elif start_opening_date:
            result = result.filter(model.Ticket.open_date >= start_opening_date)
        elif end_opening_date:
            result = result.filter(model.Ticket.open_date <= end_opening_date)

        # Closing date filter
        if closed is not None:
            if start_closing_date and end_closing_date and closed:
                result = result.filter(model.Ticket.close_date.between(start_closing_date, end_closing_date))
            elif start_closing_date and closed:
                result = result.filter(model.Ticket.close_date >= start_closing_date)
            elif end_closing_date and closed:
                result = result.filter(model.Ticket.close_date <= end_closing_date)
            elif closed:
                result = result.filter(model.Ticket.close_date.isnot(None))
            else:
                result = result.filter(model.Ticket.close_date.is_(None))

        # Entity
        if entity_id is not None:
            result = result.filter(model.Ticket.entity_id == entity_id)

        # Group
        if group_id is not None:
            group_ticket_ids = db.query(model.GroupTicket.ticket_id).filter(model.GroupTicket.group_id == group_id)
            result = result.filter(model.Ticket.id.in_([q[0] for q in group_ticket_ids]))

        # Status
        if status:
            result = result.filter(model.Ticket.status.in_(status))

        # User assignment
        if user_id:
            user_ticket_ids = db.query(model.TicketUser.ticket_id).filter(
                model.TicketUser.user_id.in_(user_id),
                model.TicketUser.type == assignment_type
            )
            result = result.filter(model.Ticket.id.in_([q[0] for q in user_ticket_ids]))

        # Name search
        if ticket_name_contains:
            result = result.filter(model.Ticket.name.contains(ticket_name_contains))

        # ITIL category
        if itil_category_id:
            result = result.filter(model.Ticket.itil_category_id.in_(itil_category_id))

        # Deleted flag
        result = result.filter(model.Ticket.is_deleted == int(is_deleted))

        # Final ordering — VERY IMPORTANT: LAST
        result = result.order_by(model.Ticket.date_modified.desc())

        # Debug print — remove in production
        print([ticket.date_modified for ticket in result.limit(5).all()])

        # Paginate
        if count_only:
            return result.count()
        return paginate(result, params=params)
