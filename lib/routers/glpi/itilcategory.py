from fastapi import HTT<PERSON>Ex<PERSON>, Query, Depends, APIRouter
from sqlalchemy.orm import Session

from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate

from database import get_glpi_db
import lib.schemas.glpi as sc
import lib.models.glpi as mdl
from lib.auth import get_current_user


TAG = 'GLPI ITIL Category'
router = APIRouter()


@router.get('/{id}', tags=[TAG], response_model=sc.ItilCategory, status_code=200)
async def get_itilcategory_by_id(id: int, 
                                 user: dict = Depends(get_current_user),
                                 db: Session = Depends(get_glpi_db)):
    q = db.query(mdl.ItilCategory).filter(mdl.ItilCategory.id == id).first()
    if q is None:
        raise HTTPException(status_code=404, detail=f'No ITILCategory found.')
    return q



@router.get('/', 
            tags=[TAG], 
            response_model=Page[sc.ItilCategory], 
            status_code=200)
async def get_ticket(user: dict = Depends(get_current_user), 
                     db: Session = Depends(get_glpi_db)):
    result = db.query(mdl.ItilCategory)
    return paginate(result)
