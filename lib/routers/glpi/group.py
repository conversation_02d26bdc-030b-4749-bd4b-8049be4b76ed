from fastapi import HTTPEx<PERSON>, Query, Depends, APIRouter
from sqlalchemy.orm import Session

from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate

from database import get_glpi_db
import lib.schemas.glpi as sc
import lib.models.glpi as mdl
from lib.auth import get_current_user


router = APIRouter()


@router.get('/{id}', tags=['GLPI Group'], response_model=sc.Group, status_code=200)
async def get_group_by_id(id: int, 
                          user: dict = Depends(get_current_user),
                          db: Session = Depends(get_glpi_db)):
    q = db.query(mdl.Group).filter(mdl.Group.id == id).first()
    if q is None:
        raise HTTPException(status_code=404, detail=f'No group found.')
    return q


@router.get('/', tags=['GLPI Group'], response_model=Page[sc.Group], status_code=200)
async def get_group(entity_id: int = None, 
                    user: dict = Depends(get_current_user),
                    db: Session = Depends(get_glpi_db)):
    q = db.query(mdl.Group)

    if entity_id is not None:
        q = q.filter(mdl.Group.entity_id == entity_id)
    return paginate(q)