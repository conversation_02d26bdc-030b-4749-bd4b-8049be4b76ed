import logging

from sqlalchemy import select, and_, any_, or_
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException, Query, Depends, APIRouter
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from fastapi_filter import FilterDepends
from typing import List
from datetime import datetime
import datetime as dt

from database import get_glpi_db
import lib.models.glpi as model
import lib.schemas.dar as schemas
import lib.crud.utils as crud_utils
from lib.auth import get_current_user


TAG = 'GLPI Reports'

router = APIRouter()

def additional_field_to_dt(additional_dt):
    logging.debug(additional_dt)
    return datetime.strptime(additional_dt, '%Y-%m-%d %H:%M:%S')
    

@router.get('/getdarentries', tags=[TAG], response_model=schemas.GLPIDarEntries, status_code=200)
async def get_dar_entries(user_id: int,
                         start_date: datetime,
                         end_date: datetime,
                         assignment_type = 2,
                         user: dict = Depends(get_current_user),
                         db: Session = Depends(get_glpi_db)):
    dates = crud_utils.generate_date_range(start_date, end_date)
    conditions = [model.TicketAdditionalField.start_time.like(f"%{one_date}%") for one_date in dates]
    result = db.query(model.TicketAdditionalField.ticket_id).filter(or_(*conditions))
    ticket_ids = [x[0] for x in result.distinct().all()]
    result = db.query(model.Ticket).filter(model.Ticket.id.in_(ticket_ids),
                                           model.Ticket.is_deleted.is_(False))

    w = result.with_entities(model.TicketUser.ticket_id) \
            .filter(model.TicketUser.user_id == user_id,
                    model.TicketUser.type == assignment_type)
    ticket_ids = [q[0] for q in w]
    result = result.filter(model.Ticket.id.in_(ticket_ids))

    dar_tickets = []
    for r in result:
        try:
            dar_tickets.append({
                'start_dt': additional_field_to_dt(r.additional_field.start_time),
                'start_date': additional_field_to_dt(r.additional_field.start_time).strftime('%B %d, %Y'),
                'start_time': additional_field_to_dt(r.additional_field.start_time).strftime('%I:%M %p'),
                'end_time': additional_field_to_dt(r.additional_field.end_time).strftime('%I:%M %p'),
                'task': r.name,
                'source_type': 'ticket',
                'source_id': r.id,
                'parent_ticket_id': None,
                'parent_ticket_title': None
            })
        except Exception as e:
            logging.error(str(r))
            logging.error(f'ticket ID: {r.id}')
            logging.error(e)
    
    # for ticket tasks
    dar_tickettasks = []
    ids = [f.id for f in db.query(model.TicketTask).all() if f.date and f.date.strftime("%Y-%m-%d") in dates]
    result = db.query(model.TicketTask).filter(model.TicketTask.id.in_(ids))
    result = result.filter(model.TicketTask.user_id == user_id)    

    for r in result:
        try: 
            task = {
                'start_dt': r.date,
                'start_date': r.date.strftime('%B %d, %Y'),
                'start_time': r.date.strftime('%I:%M %p'),
                'end_time': (r.date + dt.timedelta(0, r.duration)).strftime('%I:%M %p'),
                'task': r.short_description if r.short_description else '',
                'source_type': 'tickettask',
                'source_id': r.id,
                'parent_ticket_id': None,
                'parent_ticket_title': None
            } 
            dar_tickettasks.append(task)
        except Exception as e:
            logging.error(str(r))
            logging.error(f'task ID: {r.id}')
            logging.error(e)

    return {'entries': [*dar_tickets, *dar_tickettasks]}