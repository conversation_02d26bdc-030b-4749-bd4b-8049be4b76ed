from fastapi import APIRouter, Depends, Query, HTTPException
from datetime import datetime, timedelta
from typing import Optional, List, Dict
from sqlalchemy.orm import Session
from sqlalchemy import func, case, and_, or_, sql
from sqlalchemy import select

from lib.models import glpi as model
from database import get_glpi_db
import lib.schemas.glpi as schemas
from lib.auth import get_current_user
from lib.utils import paginate, Params


router = APIRouter()


@router.get('/group/{group_id}', tags=['GLPI Ticket Stats'])
async def get_ticket_stats(
    group_id: int,
    itil_category_id: int = Query(...),
    entity_id: Optional[int] = Query(None),
    start_date: datetime = Query(...),
    end_date: datetime = Query(...),
    is_deleted: bool = Query(False),
    user: dict = Depends(get_current_user),
    db: Session = Depends(get_glpi_db),
):
    """
    Returns ticket stats and actual tickets: open at start, closed during, new during, open at end.
    """

    # Make start_date and end_date timezone-naive for comparison
    start_date = start_date.replace(tzinfo=None)
    end_date = end_date.replace(tzinfo=None)

    # Subquery to filter tickets by group
    group_ticket_ids = db.query(model.GroupTicket.ticket_id).filter(
        model.GroupTicket.group_id == group_id,
        model.GroupTicket.type == 3 # Filter by observer group type
    ).subquery()

    # Base query filtered by group, category, (optionally entity)
    base_query = db.query(model.Ticket).filter(
        model.Ticket.id.in_(group_ticket_ids),
        model.Ticket.itil_category_id == itil_category_id,
        model.Ticket.is_deleted == is_deleted
    )
    if entity_id:
        base_query = base_query.filter(model.Ticket.entity_id == entity_id)

    tickets = base_query.all()

    # Categorized ticket lists
    open_at_start, closed_during, new_during, open_at_end = [], [], [], []

    for ticket in tickets:
        created = ticket.date
        closed = ticket.close_date if ticket.close_date else None

        if created < start_date and (closed is None or closed >= start_date):
            open_at_start.append(ticket)

        if closed and start_date <= closed <= end_date and created < end_date:
            closed_during.append(ticket)

        if start_date <= created <= end_date:
            new_during.append(ticket)

        if created <= end_date and (closed is None or closed > end_date):
            open_at_end.append(ticket)

    # Helper to convert to schema
    def serialize(tickets):
        return [schemas.Ticket.from_orm(t) for t in tickets]

    return {
        "openTicketsAtStart": {
            "count": len(open_at_start),
            "tickets": serialize(open_at_start)
        },
        "closedTicketsDuringRange": {
            "count": len(closed_during),
            "tickets": serialize(closed_during)
        },
        "newTicketsDuringRange": {
            "count": len(new_during),
            "tickets": serialize(new_during)
        },
        "openTicketsAtEnd": {
            "count": len(open_at_end),
            "tickets": serialize(open_at_end)
        }
    }

@router.get('/all_tickets', tags=['GLPI Ticket Stats'])
async def get_all_ticket_stats(
    itil_category_id: int = Query(...),
    entity_id: Optional[int] = Query(None),
    start_date: datetime = Query(...),
    end_date: datetime = Query(...),
    is_deleted: bool = Query(False),
    user: dict = Depends(get_current_user),
    db: Session = Depends(get_glpi_db),
):
    """
    Returns ticket stats for all tickets, categorized by group: open at start, closed during, new during, open at end.
    """

    start_date = start_date.replace(tzinfo=None)
    end_date = end_date.replace(tzinfo=None)

    all_groups = db.query(model.Group).all()
    group_stats = []

    for group in all_groups:
        group_ticket_ids = db.query(model.GroupTicket.ticket_id).filter(
            model.GroupTicket.group_id == group.id,
            model.GroupTicket.type == 3 # Filter by observer group type
        ).subquery()

        base_query = db.query(model.Ticket).filter(
            model.Ticket.id.in_(group_ticket_ids),
            model.Ticket.itil_category_id == itil_category_id,
            model.Ticket.is_deleted == is_deleted
        )
        if entity_id:
            base_query = base_query.filter(model.Ticket.entity_id == entity_id)

        tickets = base_query.all()

        # Only tickets that are open within the date range
        open_tickets_in_range = []
        closed_during = []
        for ticket in tickets:
            created = ticket.date
            closed = ticket.close_date if ticket.close_date else None

            # Open tickets: created <= end_date, not closed or closed after end_date, and created >= start_date
            if created <= end_date and (closed is None or closed > end_date) and created >= start_date:
                open_tickets_in_range.append(ticket)

            # Closed tickets during range: closed within range and created before end_date
            if closed and start_date <= closed <= end_date and created < end_date:
                closed_during.append(ticket)

        def serialize(tickets):
            return [schemas.Ticket.from_orm(t) for t in tickets]

        group_stats.append({
            "group": schemas.Group.model_validate({
                "id": group.id,
                "name": group.name,
                "complete_name": group.complete_name,
                "entity_id": group.entity_id,
                "group_users": []
            }),
            "openTicketsInRange": {
                "count": len(open_tickets_in_range),
                "tickets": serialize(open_tickets_in_range)
            },
            "closedTicketsDuringRange": {
                "count": len(closed_during),
                "tickets": serialize(closed_during)
            }
        })

    return group_stats

@router.get('/no_group_tickets', tags=['GLPI Ticket Stats'])
async def get_no_group_ticket_stats(
    itil_category_id: Optional[int] = None,
    entity_id: Optional[int] = Query(None),
    is_open: bool = Query(..., description="True for open tickets, False for closed tickets"),
    is_deleted: bool = Query(False),
    params: Params = Depends(),
    user: dict = Depends(get_current_user),
    db: Session = Depends(get_glpi_db),
):
    """
    Returns a count and paginated list of tickets not associated with any group,
    filtered by whether they are open or closed.
    """

    # Subquery to find all ticket_ids that are associated with any observer group
    grouped_ticket_ids_subquery = db.query(model.GroupTicket.ticket_id).filter(
        model.GroupTicket.type == 3 # Only consider observer group associations
    ).subquery()

    # Base query for tickets not in any group, filtered by category and optionally entity
    base_query = db.query(model.Ticket).filter(
        ~model.Ticket.id.in_(select(grouped_ticket_ids_subquery.c.ticket_id)),
        model.Ticket.is_deleted == is_deleted
    )

    if itil_category_id:
        base_query = base_query.filter(model.Ticket.itil_category_id == itil_category_id)
    if entity_id:
        base_query = base_query.filter(model.Ticket.entity_id == entity_id)

    # Apply filter based on is_open parameter
    if is_open:
        filtered_query = base_query.filter(model.Ticket.close_date.is_(None))
    else:
        filtered_query = base_query.filter(model.Ticket.close_date.isnot(None))

    # Get the count
    ticket_count = filtered_query.count()

    # Paginate the tickets
    paginated_tickets = paginate(filtered_query, params)

    overall_stats = schemas.NoGroupOverallTicketStats(
        count=ticket_count
    )

    return schemas.NoGroupTicketsResponse(
        overall_stats=overall_stats,
        tickets=paginated_tickets
    )

@router.get('/ticket-timeframe-counts', tags=['GLPI Ticket Stats'], response_model=List[schemas.GroupTicketTimeframeCount])
async def get_ticket_timeframe_counts(
    itil_category_id: Optional[int] = Query(None),
    entity_id: Optional[int] = Query(None),
    is_deleted: bool = Query(False),
    user: dict = Depends(get_current_user),
    db: Session = Depends(get_glpi_db),
):
    """
    Returns counts of open tickets categorized by various timeframes, grouped by group.
    """
    now = datetime.now().replace(tzinfo=None)
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)

    # Define time thresholds for non-overlapping ranges
    threshold_7_days_ago = now - timedelta(days=7)
    threshold_14_days_ago = now - timedelta(days=14)
    threshold_30_days_ago = now - timedelta(days=30)
    threshold_60_days_ago = now - timedelta(days=60)
    threshold_90_days_ago = now - timedelta(days=90)
    threshold_6_months_ago = now - timedelta(days=6*30) # Approx 6 months
    threshold_1_year_ago = now - timedelta(days=365)
    threshold_2_years_ago = now - timedelta(days=2*365)

    # Fetch all open tickets for the given filters
    tickets_query = db.query(model.Ticket).filter(
        model.Ticket.close_date.is_(None),
        model.Ticket.is_deleted == is_deleted
    )

    if itil_category_id:
        tickets_query = tickets_query.filter(model.Ticket.itil_category_id == itil_category_id)
    if entity_id:
        tickets_query = tickets_query.filter(model.Ticket.entity_id == entity_id)

    all_open_tickets = tickets_query.all()

    # Initialize counts for each group
    group_ticket_data: Dict[int, Dict[str, int]] = {}
    all_groups = db.query(model.Group).all()
    for group in all_groups:
        group_ticket_data[group.id] = {
            "today": 0,
            "past_7_days": 0,
            "past_8_to_14_days": 0,
            "past_15_to_30_days": 0,
            "past_31_to_60_days": 0,
            "past_61_to_90_days": 0,
            "past_91_days_to_6_months": 0,
            "past_6_months_to_1_year": 0,
            "past_1_to_2_years": 0,
            "older_than_2_years": 0,
        }

    # Process each ticket and categorize it
    for ticket in all_open_tickets:
        ticket_created_date = ticket.date.replace(tzinfo=None)

        # Find which groups this ticket belongs to (observer type 3)
        ticket_groups = db.query(model.GroupTicket.group_id).filter(
            model.GroupTicket.ticket_id == ticket.id,
            model.GroupTicket.type == 3
        ).all()

        for group_id_tuple in ticket_groups:
            group_id = group_id_tuple[0]
            if group_id not in group_ticket_data:
                continue

            # Categorize the ticket based on its creation date into non-overlapping bins
            if ticket_created_date >= today_start:
                group_ticket_data[group_id]["today"] += 1
            elif ticket_created_date >= threshold_7_days_ago:
                group_ticket_data[group_id]["past_7_days"] += 1
            elif ticket_created_date >= threshold_14_days_ago:
                group_ticket_data[group_id]["past_8_to_14_days"] += 1
            elif ticket_created_date >= threshold_30_days_ago:
                group_ticket_data[group_id]["past_15_to_30_days"] += 1
            elif ticket_created_date >= threshold_60_days_ago:
                group_ticket_data[group_id]["past_31_to_60_days"] += 1
            elif ticket_created_date >= threshold_90_days_ago:
                group_ticket_data[group_id]["past_61_to_90_days"] += 1
            elif ticket_created_date >= threshold_6_months_ago:
                group_ticket_data[group_id]["past_91_days_to_6_months"] += 1
            elif ticket_created_date >= threshold_1_year_ago:
                group_ticket_data[group_id]["past_6_months_to_1_year"] += 1
            elif ticket_created_date >= threshold_2_years_ago:
                group_ticket_data[group_id]["past_1_to_2_years"] += 1
            else:
                group_ticket_data[group_id]["older_than_2_years"] += 1

    # Prepare the final response
    results = []
    for group_id, counts in group_ticket_data.items():
        group_obj = next((g for g in all_groups if g.id == group_id), None)
        if group_obj:
            results.append(
                schemas.GroupTicketTimeframeCount(
                    group=schemas.Group.from_orm(group_obj),
                    counts=schemas.TicketTimeframeCounts(**counts)
                )
            )

    return results

@router.get('/group/{group_id}/ticket-details-by-timeframe', tags=['GLPI Ticket Stats'], response_model=schemas.GroupTicketTimeframeDetails)
async def get_group_ticket_details_by_timeframe(
    group_id: int,
    itil_category_id: Optional[int] = Query(None),
    entity_id: Optional[int] = Query(None),
    timeframe: Optional[int] = Query(None),
    is_deleted: bool = Query(False),
    user: dict = Depends(get_current_user),
    db: Session = Depends(get_glpi_db),
):
    """
    Returns open tickets for a specific group, categorized by various timeframes, including ticket details.
    If a timeframe is specified, only tickets for that timeframe are returned.

    Timeframe integer values for use with the 'timeframe' parameter:
        0: "today_tickets"
        1: "past_7_days_tickets"
        2: "past_8_to_14_days_tickets"
        3: "past_15_to_30_days_tickets"
        4: "past_31_to_60_days_tickets"
        5: "past_61_to_90_days_tickets"
        6: "past_91_days_to_6_months_tickets"
        7: "past_6_months_to_1_year_tickets"
        8: "past_1_to_2_years_tickets"
        9: "older_than_2_years_tickets"
    """
    now = datetime.now().replace(tzinfo=None)
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)

    # Define time thresholds for non-overlapping ranges
    threshold_7_days_ago = now - timedelta(days=7)
    threshold_14_days_ago = now - timedelta(days=14)
    threshold_30_days_ago = now - timedelta(days=30)
    threshold_60_days_ago = now - timedelta(days=60)
    threshold_90_days_ago = now - timedelta(days=90)
    threshold_6_months_ago = now - timedelta(days=6*30) # Approx 6 months
    threshold_1_year_ago = now - timedelta(days=365)
    threshold_2_years_ago = now - timedelta(days=2*365)

    # Map integer keys to timeframe details for easier passing as a parameter
    VALID_TIMEFRAME_MAPPING = {
        0: {"key": "today_tickets", "condition": lambda t_created_date: t_created_date >= today_start},
        1: {"key": "past_7_days_tickets", "condition": lambda t_created_date: t_created_date >= threshold_7_days_ago and t_created_date < today_start},
        2: {"key": "past_8_to_14_days_tickets", "condition": lambda t_created_date: t_created_date >= threshold_14_days_ago and t_created_date < threshold_7_days_ago},
        3: {"key": "past_15_to_30_days_tickets", "condition": lambda t_created_date: t_created_date >= threshold_30_days_ago and t_created_date < threshold_14_days_ago},
        4: {"key": "past_31_to_60_days_tickets", "condition": lambda t_created_date: t_created_date >= threshold_60_days_ago and t_created_date < threshold_30_days_ago},
        5: {"key": "past_61_to_90_days_tickets", "condition": lambda t_created_date: t_created_date >= threshold_90_days_ago and t_created_date < threshold_60_days_ago},
        6: {"key": "past_91_days_to_6_months_tickets", "condition": lambda t_created_date: t_created_date >= threshold_6_months_ago and t_created_date < threshold_90_days_ago},
        7: {"key": "past_6_months_to_1_year_tickets", "condition": lambda t_created_date: t_created_date >= threshold_1_year_ago and t_created_date < threshold_6_months_ago},
        8: {"key": "past_1_to_2_years_tickets", "condition": lambda t_created_date: t_created_date >= threshold_2_years_ago and t_created_date < threshold_1_year_ago},
        9: {"key": "older_than_2_years_tickets", "condition": lambda t_created_date: t_created_date < threshold_2_years_ago},
    }

    if timeframe is not None and timeframe not in VALID_TIMEFRAME_MAPPING:
        raise HTTPException(status_code=400, detail="Invalid timeframe provided")

    # Get the specific group
    group_obj = db.query(model.Group).filter(model.Group.id == group_id).first()
    if not group_obj:
        raise HTTPException(status_code=404, detail="Group not found")

    # Subquery to filter tickets by the specific group (observer type 3)
    group_ticket_ids = db.query(model.GroupTicket.ticket_id).filter(
        model.GroupTicket.group_id == group_id,
        model.GroupTicket.type == 3
    ).subquery()

    # Base query for all open tickets for this group
    base_ticket_query = db.query(model.Ticket).filter(
        model.Ticket.id.in_(group_ticket_ids),
        model.Ticket.close_date.is_(None),
        model.Ticket.is_deleted == is_deleted
    )

    if itil_category_id:
        base_ticket_query = base_ticket_query.filter(model.Ticket.itil_category_id == itil_category_id)
    if entity_id:
        base_ticket_query = base_ticket_query.filter(model.Ticket.entity_id == entity_id)

    tickets_for_group = base_ticket_query.all()

    # Initialize lists for each timeframe. All keys must be present for Pydantic validation.
    timeframe_details: Dict[str, List[schemas.Ticket]] = {
        info["key"]: [] for info in VALID_TIMEFRAME_MAPPING.values()
    }

    if timeframe is not None:
        # Process only for the specified timeframe
        requested_info = VALID_TIMEFRAME_MAPPING[timeframe]
        requested_key = requested_info["key"]
        condition_func = requested_info["condition"]

        for ticket in tickets_for_group:
            ticket_created_date = ticket.date.replace(tzinfo=None)
            serialized_ticket = schemas.Ticket.from_orm(ticket)

            if condition_func(ticket_created_date):
                timeframe_details[requested_key].append(serialized_ticket)
    else:
        # Process each ticket and categorize it (existing logic)
        for ticket in tickets_for_group:
            ticket_created_date = ticket.date.replace(tzinfo=None)
            serialized_ticket = schemas.Ticket.from_orm(ticket)

            if ticket_created_date >= today_start:
                timeframe_details["today_tickets"].append(serialized_ticket)
            elif ticket_created_date >= threshold_7_days_ago:
                timeframe_details["past_7_days_tickets"].append(serialized_ticket)
            elif ticket_created_date >= threshold_14_days_ago:
                timeframe_details["past_8_to_14_days_tickets"].append(serialized_ticket)
            elif ticket_created_date >= threshold_30_days_ago:
                timeframe_details["past_15_to_30_days_tickets"].append(serialized_ticket)
            elif ticket_created_date >= threshold_60_days_ago:
                timeframe_details["past_31_to_60_days_tickets"].append(serialized_ticket)
            elif ticket_created_date >= threshold_90_days_ago:
                timeframe_details["past_61_to_90_days_tickets"].append(serialized_ticket)
            elif ticket_created_date >= threshold_6_months_ago:
                timeframe_details["past_91_days_to_6_months_tickets"].append(serialized_ticket)
            elif ticket_created_date >= threshold_1_year_ago:
                timeframe_details["past_6_months_to_1_year_tickets"].append(serialized_ticket)
            elif ticket_created_date >= threshold_2_years_ago:
                timeframe_details["past_1_to_2_years_tickets"].append(serialized_ticket)
            else:
                timeframe_details["older_than_2_years_tickets"].append(serialized_ticket)

    return schemas.GroupTicketTimeframeDetails(
        group=schemas.Group.from_orm(group_obj),
        details=schemas.TicketTimeframeDetails(**timeframe_details)
    )

@router.get('/inactive-ticket-timeframe-counts', tags=['GLPI Ticket Stats'], response_model=List[schemas.GroupTicketTimeframeCount])
async def get_inactive_ticket_timeframe_counts(
    itil_category_id: Optional[int] = Query(None),
    entity_id: Optional[int] = Query(None),
    is_deleted: bool = Query(False),
    user: dict = Depends(get_current_user),
    db: Session = Depends(get_glpi_db),
):
    """
    Returns counts of open inactive tickets categorized by last modified date timeframes, grouped by group.

    Timeframe integer values for use with the 'timeframe' parameter:
        0: "today_tickets"
        1: "past_7_days_tickets"
        2: "past_8_to_14_days_tickets"
        3: "past_15_to_30_days_tickets"
        4: "past_31_to_60_days_tickets"
        5: "past_61_to_90_days_tickets"
        6: "past_91_days_to_6_months_tickets"
        7: "past_6_months_to_1_year_tickets"
        8: "past_1_to_2_years_tickets"
        9: "older_than_2_years_tickets"
    """
    now = datetime.now().replace(tzinfo=None)
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)

    # Define time thresholds for non-overlapping ranges
    threshold_7_days_ago = now - timedelta(days=7)
    threshold_14_days_ago = now - timedelta(days=14)
    threshold_30_days_ago = now - timedelta(days=30)
    threshold_60_days_ago = now - timedelta(days=60)
    threshold_90_days_ago = now - timedelta(days=90)
    threshold_6_months_ago = now - timedelta(days=6*30) # Approx 6 months
    threshold_1_year_ago = now - timedelta(days=365)
    threshold_2_years_ago = now - timedelta(days=2*365)

    # Fetch all inactive open tickets for the given filters
    tickets_query = db.query(model.Ticket).filter(
        model.Ticket.close_date.is_(None),
        model.Ticket.is_deleted == is_deleted
    )

    if itil_category_id:
        tickets_query = tickets_query.filter(model.Ticket.itil_category_id == itil_category_id)
    if entity_id:
        tickets_query = tickets_query.filter(model.Ticket.entity_id == entity_id)

    all_inactive_tickets = tickets_query.all()

    # Initialize counts for each group
    group_ticket_data: Dict[int, Dict[str, int]] = {}
    all_groups = db.query(model.Group).all()
    for group in all_groups:
        group_ticket_data[group.id] = {
            "today": 0,
            "past_7_days": 0,
            "past_8_to_14_days": 0,
            "past_15_to_30_days": 0,
            "past_31_to_60_days": 0,
            "past_61_to_90_days": 0,
            "past_91_days_to_6_months": 0,
            "past_6_months_to_1_year": 0,
            "past_1_to_2_years": 0,
            "older_than_2_years": 0,
        }

    # Process each ticket and categorize it using date_modified
    for ticket in all_inactive_tickets:
        ticket_modified_date = ticket.date_modified.replace(tzinfo=None)

        # Find which groups this ticket belongs to (observer type 3)
        ticket_groups = db.query(model.GroupTicket.group_id).filter(
            model.GroupTicket.ticket_id == ticket.id,
            model.GroupTicket.type == 3
        ).all()

        for group_id_tuple in ticket_groups:
            group_id = group_id_tuple[0]
            if group_id not in group_ticket_data:
                continue

            # Categorize the ticket based on its modified date into non-overlapping bins
            if ticket_modified_date >= today_start:
                group_ticket_data[group_id]["today"] += 1
            elif ticket_modified_date >= threshold_7_days_ago:
                group_ticket_data[group_id]["past_7_days"] += 1
            elif ticket_modified_date >= threshold_14_days_ago:
                group_ticket_data[group_id]["past_8_to_14_days"] += 1
            elif ticket_modified_date >= threshold_30_days_ago:
                group_ticket_data[group_id]["past_15_to_30_days"] += 1
            elif ticket_modified_date >= threshold_60_days_ago:
                group_ticket_data[group_id]["past_31_to_60_days"] += 1
            elif ticket_modified_date >= threshold_90_days_ago:
                group_ticket_data[group_id]["past_61_to_90_days"] += 1
            elif ticket_modified_date >= threshold_6_months_ago:
                group_ticket_data[group_id]["past_91_days_to_6_months"] += 1
            elif ticket_modified_date >= threshold_1_year_ago:
                group_ticket_data[group_id]["past_6_months_to_1_year"] += 1
            elif ticket_modified_date >= threshold_2_years_ago:
                group_ticket_data[group_id]["past_1_to_2_years"] += 1
            else:
                group_ticket_data[group_id]["older_than_2_years"] += 1

    # Prepare the final response
    results = []
    for group_id, counts in group_ticket_data.items():
        group_obj = next((g for g in all_groups if g.id == group_id), None)
        if group_obj:
            results.append(
                schemas.GroupTicketTimeframeCount(
                    group=schemas.Group.from_orm(group_obj),
                    counts=schemas.TicketTimeframeCounts(**counts)
                )
            )

    return results

@router.get('/group/{group_id}/inactive-ticket-details-by-timeframe', tags=['GLPI Ticket Stats'], response_model=schemas.GroupTicketTimeframeDetails)
async def get_group_inactive_ticket_details_by_timeframe(
    group_id: int,
    itil_category_id: Optional[int] = Query(None),
    entity_id: Optional[int] = Query(None),
    timeframe: Optional[int] = Query(None),
    is_deleted: bool = Query(False),
    user: dict = Depends(get_current_user),
    db: Session = Depends(get_glpi_db),
):
    """
    Returns open inactive tickets for a specific group, categorized by last modified date timeframes, including ticket details.
    If a timeframe is specified, only tickets for that timeframe are returned.

    Timeframe integer values for use with the 'timeframe' parameter:
        0: "today_tickets"
        1: "past_7_days_tickets"
        2: "past_8_to_14_days_tickets"
        3: "past_15_to_30_days_tickets"
        4: "past_31_to_60_days_tickets"
        5: "past_61_to_90_days_tickets"
        6: "past_91_days_to_6_months_tickets"
        7: "past_6_months_to_1_year_tickets"
        8: "past_1_to_2_years_tickets"
        9: "older_than_2_years_tickets"
    """
    now = datetime.now().replace(tzinfo=None)
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)

    # Define time thresholds for non-overlapping ranges
    threshold_7_days_ago = now - timedelta(days=7)
    threshold_14_days_ago = now - timedelta(days=14)
    threshold_30_days_ago = now - timedelta(days=30)
    threshold_60_days_ago = now - timedelta(days=60)
    threshold_90_days_ago = now - timedelta(days=90)
    threshold_6_months_ago = now - timedelta(days=6*30) # Approx 6 months
    threshold_1_year_ago = now - timedelta(days=365)
    threshold_2_years_ago = now - timedelta(days=2*365)

    # Map integer keys to timeframe details for easier passing as a parameter
    VALID_TIMEFRAME_MAPPING = {
        0: {"key": "today_tickets", "condition": lambda t_mod_date: t_mod_date >= today_start},
        1: {"key": "past_7_days_tickets", "condition": lambda t_mod_date: t_mod_date >= threshold_7_days_ago and t_mod_date < today_start},
        2: {"key": "past_8_to_14_days_tickets", "condition": lambda t_mod_date: t_mod_date >= threshold_14_days_ago and t_mod_date < threshold_7_days_ago},
        3: {"key": "past_15_to_30_days_tickets", "condition": lambda t_mod_date: t_mod_date >= threshold_30_days_ago and t_mod_date < threshold_14_days_ago},
        4: {"key": "past_31_to_60_days_tickets", "condition": lambda t_mod_date: t_mod_date >= threshold_60_days_ago and t_mod_date < threshold_30_days_ago},
        5: {"key": "past_61_to_90_days_tickets", "condition": lambda t_mod_date: t_mod_date >= threshold_90_days_ago and t_mod_date < threshold_60_days_ago},
        6: {"key": "past_91_days_to_6_months_tickets", "condition": lambda t_mod_date: t_mod_date >= threshold_6_months_ago and t_mod_date < threshold_90_days_ago},
        7: {"key": "past_6_months_to_1_year_tickets", "condition": lambda t_mod_date: t_mod_date >= threshold_1_year_ago and t_mod_date < threshold_6_months_ago},
        8: {"key": "past_1_to_2_years_tickets", "condition": lambda t_mod_date: t_mod_date >= threshold_2_years_ago and t_mod_date < threshold_1_year_ago},
        9: {"key": "older_than_2_years_tickets", "condition": lambda t_mod_date: t_mod_date < threshold_2_years_ago},
    }

    if timeframe is not None and timeframe not in VALID_TIMEFRAME_MAPPING:
        raise HTTPException(status_code=400, detail="Invalid timeframe provided")

    # Get the specific group
    group_obj = db.query(model.Group).filter(model.Group.id == group_id).first()
    if not group_obj:
        raise HTTPException(status_code=404, detail="Group not found")

    # Subquery to filter tickets by the specific group (observer type 3)
    group_ticket_ids = db.query(model.GroupTicket.ticket_id).filter(
        model.GroupTicket.group_id == group_id,
        model.GroupTicket.type == 3
    ).subquery()

    # Base query for inactive tickets, filtered by group, category, and optionally entity, and ensuring tickets are open
    base_ticket_query = db.query(model.Ticket).filter(
        model.Ticket.id.in_(group_ticket_ids),
        model.Ticket.close_date.is_(None),
        model.Ticket.is_deleted == is_deleted
    )

    if itil_category_id:
        base_ticket_query = base_ticket_query.filter(model.Ticket.itil_category_id == itil_category_id)
    if entity_id:
        base_ticket_query = base_ticket_query.filter(model.Ticket.entity_id == entity_id)

    tickets_for_group = base_ticket_query.all()

    # Initialize lists for each timeframe. All keys must be present for Pydantic validation.
    timeframe_details: Dict[str, List[schemas.Ticket]] = {
        info["key"]: [] for info in VALID_TIMEFRAME_MAPPING.values()
    }

    if timeframe is not None:
        # Process only for the specified timeframe
        requested_info = VALID_TIMEFRAME_MAPPING[timeframe]
        requested_key = requested_info["key"]
        condition_func = requested_info["condition"]

        for ticket in tickets_for_group:
            ticket_modified_date = ticket.date_modified.replace(tzinfo=None)
            serialized_ticket = schemas.Ticket.from_orm(ticket)

            if condition_func(ticket_modified_date):
                timeframe_details[requested_key].append(serialized_ticket)
    else:
        # Process each ticket and categorize it using date_modified (existing logic)
        for ticket in tickets_for_group:
            ticket_modified_date = ticket.date_modified.replace(tzinfo=None)
            serialized_ticket = schemas.Ticket.from_orm(ticket)

            if ticket_modified_date >= today_start:
                timeframe_details["today_tickets"].append(serialized_ticket)
            elif ticket_modified_date >= threshold_7_days_ago:
                timeframe_details["past_7_days_tickets"].append(serialized_ticket)
            elif ticket_modified_date >= threshold_14_days_ago:
                timeframe_details["past_8_to_14_days_tickets"].append(serialized_ticket)
            elif ticket_modified_date >= threshold_30_days_ago:
                timeframe_details["past_15_to_30_days_tickets"].append(serialized_ticket)
            elif ticket_modified_date >= threshold_60_days_ago:
                timeframe_details["past_31_to_60_days_tickets"].append(serialized_ticket)
            elif ticket_modified_date >= threshold_90_days_ago:
                timeframe_details["past_61_to_90_days_tickets"].append(serialized_ticket)
            elif ticket_modified_date >= threshold_6_months_ago:
                timeframe_details["past_91_days_to_6_months_tickets"].append(serialized_ticket)
            elif ticket_modified_date >= threshold_1_year_ago:
                timeframe_details["past_6_months_to_1_year_tickets"].append(serialized_ticket)
            elif ticket_modified_date >= threshold_2_years_ago:
                timeframe_details["past_1_to_2_years_tickets"].append(serialized_ticket)
            else:
                timeframe_details["older_than_2_years_tickets"].append(serialized_ticket)

    return schemas.GroupTicketTimeframeDetails(
        group=schemas.Group.from_orm(group_obj),
        details=schemas.TicketTimeframeDetails(**timeframe_details)
    )
