import json
from fastapi import HTTPException, Query, Depends, APIRouter
from sqlalchemy.orm import Session

from database import get_glpi_db
import lib.schemas.glpi as sc
from lib.auth import get_current_user

router = APIRouter()

@router.get('/', tags=['Settings'], response_model=sc.Settings, status_code=200)
async def get_settings(user: dict = Depends(get_current_user), db: Session = Depends(get_glpi_db)):
    with open('settings.json') as f:
        return json.load(f)

