
from fastapi import APIRouter, Depends, HTTPException, Body, Query
from typing import Optional, List
import requests
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import Session
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from datetime import datetime

from database import get_glpi_db
import lib.schemas.glpi as schemas
import lib.models.glpi as model
import lib.crud.ticket as crud_ticket
import lib.filters as filters
import lib.crud.utils as crud_utils
from lib.auth import get_current_user
from uuid import uuid4

TAG = 'GLPI Ticket Task'
router = APIRouter()

@router.post('/add_task', tags=[TAG])
async def add_ticket_task(
    tickets_id: int = Query(..., description="ID of the ticket to add the task to"),
    content: str = Query(..., description="Task description"),
    start_date: datetime = Query(..., description="Task start date/time (ISO format)"),
    end_date: datetime = Query(..., description="Task end date/time (ISO format)"),
    state: int = Query(..., description="Task status (1, 2, 3, etc.)"),
    groups_id_TicketTask: int = Query(..., description="Group ID for TicketTask (groups_id_tech)"),
    user: dict = Depends(get_current_user),
    db: Session = Depends(get_glpi_db)
):
    """
    Add a task to an existing ticket (local DB only, authenticated).
    """
    # Example: create and save a TicketTask in your own database
    if not user or not user.get('id'):
        raise HTTPException(status_code=401, detail="User authentication required")
    # Calculate duration in seconds
    duration = int((end_date - start_date).total_seconds())
    now = datetime.now()
    # Insert into glpi_tickettasks
    new_task = model.TicketTask(
        uuid=str(uuid4()),
        ticket_id=tickets_id,
        content=content,
        duration=duration,
        user_id=user['id'],
        technician_id=user['id'],
        group_id=groups_id_TicketTask,
        date=start_date,
        date_mod=now,
        date_creation=now,
        users_id_editor=0,
        is_private=0,
        taskcategories_id=0,
        state=state,
        timeline_position=1,
        sourceitems_id=0,
        sourceof_items_id=0
    )
    db.add(new_task)
    db.commit()
    db.refresh(new_task)

    # Insert into glpi_logs
    new_log = model.Log(
        itemtype="Ticket",
        items_id=tickets_id,
        itemtype_link="TicketTask",
        linked_action=17,
        user_name=user.get('name') if user and user.get('name') else (f"User {user['id']}" if user and user.get('id') else "Unknown user"),
        date_mod=now,
        id_search_option=0,
        old_value="",
        new_value=str(new_task.id)
    )
    db.add(new_log)


    new_event = model.Event(
        items_id=tickets_id,
        type="ticket",
        date=now,
        service="tracking",
        level=4,
        message=(f"{user.get('name')} added a task" if user and user.get('name') else (f"User {user['id']} added a task" if user and user.get('id') else "A task was added")),

    )
    db.add(new_event)

    db.commit()
    # Get the user object for the response
    user_obj = db.query(model.User).filter(model.User.id == user['id']).first()
    if not user_obj:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Convert the task to a dict and add the user object
    task_dict = {
        'id': new_task.id,
        'ticket_id': new_task.ticket_id,
        'ticket_name': new_task.ticket.name if new_task.ticket else None,
        'content': new_task.content,
        'date': new_task.date,
        'duration': new_task.duration,
        'duration_hr': new_task.duration_hr if hasattr(new_task, 'duration_hr') else (new_task.duration / 3600.0 if new_task.duration else 0),
        'user': user_obj,
        'technician_id': new_task.technician_id,
        'group_id': new_task.group_id,
        'state': new_task.state
    }
    
    # Create the response using the schema
    return schemas.TicketTask(**task_dict)

@router.get('/{id}', tags=[TAG], response_model=schemas.TicketTask, status_code=200)
async def get_tickettask_by_id(id: int, user: dict = Depends(get_current_user), db: Session = Depends(get_glpi_db)):
    q = db.query(model.TicketTask).filter(model.TicketTask.id == id).first()
    if q is None:
        raise HTTPException(status_code=404, detail='No item found.')
    return q


@router.get('/', tags=[TAG], response_model=Page[schemas.TicketTask], status_code=200)
async def get_ticket_task(
    user_id: int = None,
    ticket_name_contains: str = None,
    start_date: datetime = None,
    end_date: datetime = None,
    user: dict = Depends(get_current_user),
    db: Session = Depends(get_glpi_db)
):
    result = db.query(model.TicketTask)
    if start_date is not None and end_date is not None:
        dates = crud_utils.generate_date_range(start_date, end_date)
        ids = [f.id for f in db.query(model.TicketTask).all() if f.start_date in dates]
        result = result.filter(model.TicketTask.id.in_(ids))
    if user_id is not None:
        result = result.filter(model.TicketTask.user_id == user_id)
    if ticket_name_contains is not None:
        ticket_ids = [q.id for q in db.query(model.Ticket).filter(model.Ticket.name.contains(ticket_name_contains))]
        result = result.filter(model.TicketTask.ticket_id.in_(ticket_ids))
    return paginate(result)