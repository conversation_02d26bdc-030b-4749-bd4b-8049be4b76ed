from datetime import datetime, timedelta
from fastapi import HTT<PERSON>Ex<PERSON>, Query, Depends, APIRouter, status
from sqlalchemy import delete
from sqlalchemy.orm import Session

from fastapi.security import OAuth2PasswordRequestForm
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate

from database import get_glpi_db

import lib.schemas.glpi as sc
import lib.models.glpi as mdl
from lib.auth import (
    get_current_user, create_access_token, 
    authenticate_user, ACCESS_TOKEN_EXPIRE_MINUTES
)


router = APIRouter()


@router.post('/login', tags=['GLPI Auth'], response_model=sc.Token, status_code=200)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_glpi_db)):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail='Incorrect username or password',
            headers={'WWW-Authenticate': 'Bearer'},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={'sub': user.name}, expires_delta=access_token_expires
    )
    return {'access_token': access_token, 'token_type': 'bearer'}


@router.get('/me', tags=['GLPI Auth'], response_model=sc.User, status_code=200)
async def read_users_me(current_user: dict = Depends(get_current_user), db: Session = Depends(get_glpi_db)):
    user_obj = db.query(mdl.User).filter(mdl.User.id == current_user['id']).first()
    return sc.User(
        id=user_obj.id,
        name=user_obj.name,
        first_name=user_obj.first_name,
        last_name=user_obj.last_name,
        username=user_obj.name
    )