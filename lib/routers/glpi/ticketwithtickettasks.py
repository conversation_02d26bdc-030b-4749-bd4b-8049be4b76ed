from sqlalchemy import select, and_, any_, or_
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException, Query, Depends, APIRouter
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from fastapi_filter import FilterDepends
from typing import List
from datetime import datetime

from database import get_glpi_db
import lib.schemas.glpi as schemas
import lib.models.glpi as model
import lib.crud.ticket as crud_ticket
import lib.filters as filters
import lib.crud.utils as crud_utils
from lib.auth import get_current_user


TAG = 'GLPI Ticket with TicketTasks'
router = APIRouter()


@router.get('/{id}', tags=[TAG], response_model=schemas.TicketWithTicketTasks, status_code=200)
async def get_ticket_with_tickettasks_by_id(id: int, 
                                            user: dict = Depends(get_current_user),
                                            db: Session = Depends(get_glpi_db)):
    db_ent = crud_ticket.get_ticket_by_id(db, id=id)
    if db_ent is None:
        raise HTTPException(
            status_code=404, detail=f'No item found.'
        )
    return db_ent


@router.get('/', 
            tags=[TAG], 
            response_model=Page[schemas.TicketWithTicketTasks], 
            status_code=200)
async def get_ticket(user_id: int = None, 
               assignment_type: int = 2, 
               ticket_name_contains: str = None,
               itil_category_id: int = None,
               entity_id: int = None,
               group_id: int = None,
               start_opening_date: datetime = None,
               end_opening_date: datetime = None,
               closed: bool = None,
               start_closing_date: datetime = None,
               end_closing_date: datetime = None,
               start_date: datetime = None,
               end_date: datetime = None,
               user: dict = Depends(get_current_user),
               db: Session = Depends(get_glpi_db)):
    '''
    Assignment Types:\n
    1 - requester    \n
    2 - assigned to  \n
    3 - observer     \n 
    '''

    if start_date is not None and end_date is not None:
        dates = crud_utils.generate_date_range(start_date, end_date)
        conditions = [model.TicketAdditionalField.start_time.like(f"%{one_date}%") for one_date in dates]
        result = db.query(model.TicketAdditionalField.ticket_id).filter(or_(*conditions))
        ticket_ids = [x[0] for x in result.distinct().all()]
        result = db.query(model.Ticket).filter(model.Ticket.id.in_(ticket_ids))
    else:
        result = crud_ticket.get_all_tickets(db)

    if start_opening_date is not None and end_opening_date is not None:
        result = result.filter(model.Ticket.open_date.between(start_opening_date, end_opening_date))
    elif start_opening_date is not None and end_opening_date is None:
        result = result.filter(model.Ticket.open_date >= start_opening_date)
    elif start_opening_date is None and end_opening_date is not None:
        result = result.filter(model.Ticket.open_date <= end_opening_date)        

    if (start_closing_date is not None and 
        end_closing_date is None and closed):
        result = result.filter(model.Ticket.close_date >= start_closing_date)
    elif (start_closing_date is None and 
        end_closing_date is not None and closed):
        result = result.filter(model.Ticket.close_date <= end_closing_date)        
    elif (start_closing_date is not None and 
        end_closing_date is not None and closed):
        result = result.filter(model.Ticket.close_date.between(start_closing_date, end_closing_date))
    elif closed is not None and closed:
        result = result.filter(model.Ticket.close_date != None)        
    elif closed is not None and not closed:
        result = result.filter(model.Ticket.close_date == None)

    if entity_id is not None:
        result = result.filter(model.Ticket.entity_id == entity_id)

    if group_id is not None:
        grptickets = db.query(model.GroupTicket).filter(model.GroupTicket.group_id == group_id)
        ticket_ids = [q.ticket_id for q in grptickets]
        result = result.filter(model.Ticket.id.in_(ticket_ids))
    
    if user_id is not None:
        w = result.with_entities(model.TicketUser.ticket_id) \
                       .filter(model.TicketUser.user_id == user_id,
                               model.TicketUser.type == assignment_type)
        ticket_ids = [q[0] for q in w]
        result = result.filter(model.Ticket.id.in_(ticket_ids))

    if ticket_name_contains is not None:
        result = result.filter(model.Ticket.name.contains(ticket_name_contains))

    if itil_category_id is not None:
        result = result.filter(model.Ticket.itil_category_id == itil_category_id)

    return paginate(result)