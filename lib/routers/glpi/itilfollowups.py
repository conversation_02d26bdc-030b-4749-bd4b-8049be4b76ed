from sqlalchemy import select, and_, any_, or_
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPEx<PERSON>, Query, Depends, APIRouter
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from fastapi_filter import FilterDepends
from typing import List
from datetime import datetime

from database import get_glpi_db
import lib.schemas.glpi as schemas
import lib.models.glpi as model
import lib.crud.ticket as crud_ticket
import lib.filters as filters
import lib.crud.utils as crud_utils
from lib.auth import get_current_user


TAG = 'GLPI ITIL Followup'
router = APIRouter()


@router.get('/{id}', 
            tags=[TAG], 
            response_model=schemas.ItilFollowups, 
            status_code=200)
async def get_itil_followup_by_id(id: int, 
                                  user: dict = Depends(get_current_user), 
                                  db: Session = Depends(get_glpi_db)):
    q = db.query(model.ItilFollowups).filter(model.ItilFollowups.id == id).first()
    if q is None:
        raise HTTPException(
            status_code=404, detail=f'No item found.'
        )
    return q


@router.get('/', 
            tags=[TAG], 
            response_model=Page[schemas.ItilFollowups], 
            status_code=200)
async def get_itil_followup(user_id: int = None, 
                      start_date: datetime = None,
                      end_date: datetime = None,
                      user: dict = Depends(get_current_user),
                      db: Session = Depends(get_glpi_db)):

    result = db.query(model.ItilFollowups)
    
    if start_date is not None and end_date is not None:
        dates = crud_utils.generate_date_range(start_date, end_date)
        print(dates)
        ids = [f.id for f in db.query(model.ItilFollowups).all() if f.start_date in dates]
        result = result.filter(model.ItilFollowups.id.in_(ids))    
    
    if user_id is not None:
        result = result.filter(model.ItilFollowups.user_id == user_id)

    return paginate(result)