from fastapi import HTT<PERSON><PERSON>x<PERSON>, Query, Depends, APIRouter
from sqlalchemy.orm import Session

from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate

from database import get_glpi_db
import lib.schemas.glpi as sc
import lib.models.glpi as mdl
import lib.crud.entity as crud_ent
from lib.auth import get_current_user


router = APIRouter()


@router.get('/{id}', tags=['GLPI Entity'], response_model=sc.Entity, status_code=200)
async def get_entity_by_id(id: int, 
                           user: dict = Depends(get_current_user),
                           db: Session = Depends(get_glpi_db)):
    db_ent = crud_ent.get_ticket_by_id(db, id=id)
    if db_ent is None:
        raise HTTPException(
            status_code=404, detail=f'No entity found.'
        )

    return db_ent


@router.get('/{id}/tickets', tags=['GLPI Entity'], response_model=Page[sc.Ticket], status_code=200)
async def get_tickets_of_entity(id: int, 
                                user: dict = Depends(get_current_user),
                                db: Session = Depends(get_glpi_db)):
    db_ent = crud_ent.get_ticket_by_id(db, id=id)
    if db_ent is None:
        raise HTTPException(
            status_code=404, detail=f'No entity found.'
        )
    q = crud_ent.get_tickets_of_entity(db, id=id)
    return paginate(q)