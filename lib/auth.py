from fastapi import Depends, HTTPException
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from passlib.context import Cry<PERSON><PERSON>ontext
from sqlalchemy.orm import Session
from datetime import timezone, datetime, timedelta
from jose import JWTError, jwt

from lib.models.glpi import User


SECRET_KEY = 'this is my/your secret key 121231'
ALGORITHM = 'HS256'
ACCESS_TOKEN_EXPIRE_MINUTES = 1440


oauth2_scheme = OAuth2PasswordBearer(tokenUrl='auth/login')
pwd_context = CryptContext(schemes=['bcrypt'], deprecated='auto')


def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)


def get_user(db: Session, username: str):
    return db.query(User).filter(User.name == username).first()


def authenticate_user(db: Session, username: str, password: str):
    user = get_user(db, username)
    if not user:
        return None
    if not verify_password(password, user.password):
        return None
    return user


def create_access_token(data: dict, expires_delta: timedelta | None = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
    to_encode.update({'exp': expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


from database import get_glpi_db

def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_glpi_db)):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get('sub')
        if username is None:
            raise HTTPException(status_code=401, detail='Invalid authentication credentials')
        user = db.query(User).filter(User.name == username).first()
        if user is None:
            raise HTTPException(status_code=401, detail='User not found')
        return {'id': user.id, 'username': user.name}
    except JWTError:
        raise HTTPException(status_code=401, detail='Invalid authentication credentials')