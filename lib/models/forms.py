from sqlalchemy import Column, Inte<PERSON>, String, <PERSON><PERSON>ey, func, or_
from sqlalchemy.orm import relationship
from database import Base

class Question(Base):
    __tablename__ = 'glpi_plugin_formcreator_questions'
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True)
    name = Column(String)
    plugin_formcreator_sections_id = Column(Integer)

class Answer(Base):
    __tablename__ = 'glpi_plugin_formcreator_answers'
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True)
    plugin_formcreator_questions_id = Column(Integer, ForeignKey('glpi_plugin_formcreator_questions.id'))
    plugin_formcreator_formanswers_id = Column(Integer, ForeignKey('glpi_plugin_formcreator_formanswers.id'))
    answer = Column(String)

    question = relationship('Question')

class FormCategory(Base):
    __tablename__ = 'glpi_plugin_formcreator_categories'
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True)
    plugin_formcreator_categories_id = Column(Integer)
    name = Column(String)
    comment = Column(String)
    completename = Column(String)  # Added completename column

class Form(Base):
    __tablename__ = 'glpi_plugin_formcreator_forms'
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True)
    name = Column(String)
    plugin_formcreator_categories_id = Column(Integer)
    
    category = relationship(
        'FormCategory',
        primaryjoin="Form.plugin_formcreator_categories_id == FormCategory.plugin_formcreator_categories_id",
        foreign_keys="Form.plugin_formcreator_categories_id"
    )

class FormAnswer(Base):
    __tablename__ = 'glpi_plugin_formcreator_formanswers'
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True)
    name = Column(String)
    entities_id = Column(Integer)
    plugin_formcreator_forms_id = Column(Integer, ForeignKey('glpi_plugin_formcreator_forms.id'))
    requester_id = Column(Integer)
    request_date = Column(String)  # Add this field to match the database table
    groups_id_validator = Column(Integer)

    form = relationship('Form', 
                       foreign_keys=[plugin_formcreator_forms_id],
                       primaryjoin="FormAnswer.plugin_formcreator_forms_id == Form.id")
