from sqlalchemy import Column, Integer, String, Float, BigInteger, DateTime, ForeignKey, CHAR
from sqlalchemy.ext.hybrid import hybrid_property, hybrid_method
from database import Base
from sqlalchemy.orm import relationship
from bs4 import BeautifulSoup
import datetime
import html
from uuid import uuid4

class Entity(Base):
    __tablename__ = 'glpi_entities'
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True)
    name = Column(String(255))
    complete_name = Column('completename', String)
    comment = Column('comment', String)

    tickets = relationship('Ticket', back_populates='entity')
    itil_categories = relationship('ItilCategory', back_populates='entity')


class User(Base):
    __tablename__ = 'glpi_users'
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True)
    name = Column(String(255))
    first_name = Column('firstname', String)
    last_name = Column('realname', String)
    password = Column(String(255))


class Ticket(Base):
    __tablename__ = 'glpi_tickets'
    
    id = Column(Integer, primary_key=True)

    name = Column(String(255))
    status = Column(Integer)
    content = Column(String)
    urgency = Column(Integer)
    impact = Column(Integer)
    priority = Column(Integer)

    date = Column(DateTime)
    date_modified = Column('date_mod', DateTime)
    close_date = Column('closedate', DateTime)
    solve_date = Column('solvedate', DateTime)
    open_date = Column('date_creation', DateTime)

    is_deleted = Column('is_deleted', Integer)

    ticket_users = relationship('TicketUser', back_populates='ticket')
    ticket_tasks = relationship('TicketTask', back_populates='ticket')    
    observer_group_tickets = relationship(
        'GroupTicket',
        primaryjoin="and_(Ticket.id == GroupTicket.ticket_id, GroupTicket.type == 3)",
        viewonly=True # This makes it a read-only relationship, not affecting writes
    )

    entity_id = Column('entities_id', Integer, ForeignKey('glpi_entities.id'))
    entity = relationship('Entity', back_populates='tickets')

    itil_category_id = Column('itilcategories_id', Integer, ForeignKey('glpi_itilcategories.id'))
    itil_category = relationship('ItilCategory', back_populates='tickets')

    additional_field = relationship('TicketAdditionalField', uselist=False, back_populates='ticket')

    @hybrid_property
    def observer_groups(self):
        return [gt.group for gt in self.observer_group_tickets if gt.group]

    @hybrid_property
    def duration(self):
        if not self.additional_field or not self.additional_field.start_time \
            or not self.additional_field.end_time:
            return None

        format_str = "%Y-%m-%d %H:%M:%S"
        try:
            start_time_dt = datetime.datetime.strptime(self.additional_field.start_time, format_str)
            end_time_dt = datetime.datetime.strptime(self.additional_field.end_time, format_str)
            if start_time_dt > end_time_dt:
                return None
            duration_dt = end_time_dt - start_time_dt
            return duration_dt.total_seconds() / 3600
        except ValueError as err:
            return None


class TicketUser(Base):
    __tablename__ = 'glpi_tickets_users'
    
    id = Column(Integer, primary_key=True)
    
    user_id = Column('users_id', Integer, ForeignKey('glpi_users.id'))
    user = relationship('User')
    type = Column(Integer)

    ticket_id = Column('tickets_id', Integer, ForeignKey('glpi_tickets.id'))
    ticket = relationship('Ticket', back_populates='ticket_users')    

    # type
    # 1 - requester
    # 2 - assigned to
    # 3 - observer


class ItilCategory(Base):
    __tablename__ = 'glpi_itilcategories'

    id = Column(Integer, primary_key=True)

    is_recursive = Column(Integer)
    name = Column(String)
    complete_name = Column('completename', String)
    comment = Column(String)    

    tickets = relationship('Ticket', back_populates='itil_category')

    entity_id = Column('entities_id', Integer, ForeignKey('glpi_entities.id'))
    entity = relationship('Entity')


class GroupTicket(Base):
    __tablename__ = 'glpi_groups_tickets'
    
    id = Column(Integer, primary_key=True)
    
    type = Column(Integer)
    group_id = Column('groups_id', Integer, ForeignKey('glpi_groups.id'))
    group = relationship('Group')
    ticket_id = Column('tickets_id', Integer, ForeignKey('glpi_tickets.id'))
    ticket = relationship('Ticket')        


class Group(Base):
    __tablename__ = 'glpi_groups'
    
    id = Column(Integer, primary_key=True)

    is_recursive = Column(Integer)
    name = Column(String)
    complete_name = Column('completename', String)
    comment = Column(String)  

    entity_id = Column('entities_id', Integer, ForeignKey('glpi_entities.id'))
    entity = relationship('Entity')    

    group_users = relationship('GroupUser', back_populates='group')



class GroupUser(Base):
    __tablename__ = 'glpi_groups_users'
    
    id = Column(Integer, primary_key=True)

    user_id = Column('users_id', Integer, ForeignKey('glpi_users.id'))
    user = relationship('User')    

    group_id = Column('groups_id', Integer, ForeignKey('glpi_groups.id'))
    group = relationship('Group', back_populates='group_users')        



class TicketAdditionalField(Base):
    __tablename__ = 'glpi_plugin_fields_ticketvems'

    id = Column(Integer, primary_key=True)

    vem_num = Column('vemfield', String, nullable=True)
    aevm_num = Column('aevmfield', String, nullable=True)
    atg_num = Column('atgfield', String, nullable=True)
    start_time = Column('starttimefield', String, nullable=True)
    end_time = Column('endtimefield', String, nullable=True)

    ticket_id = Column('items_id', Integer, ForeignKey('glpi_tickets.id')) 
    ticket = relationship('Ticket', back_populates='additional_field')


def get_div(content, classname):
    content_unescaped = html.unescape(content)
    soup = BeautifulSoup(content_unescaped, 'html.parser')
    qdiv = soup.find('div', class_=classname)
    if qdiv is None:
        return None
    else:
        return qdiv


def get_p_content(content, classname):
    content_unescaped = html.unescape(content)
    soup = BeautifulSoup(content_unescaped, 'html.parser')
    qdiv = soup.find('div', class_=classname)

    if qdiv is None:
        return None
    all_p = qdiv.find_all('p')
    update = ''
    for p in all_p:
        update = f'{update} {p.get_text()}'.strip()
    return update


class ItilFollowups(Base):
    __tablename__ = 'glpi_itilfollowups'

    id = Column(Integer, primary_key=True)

    content = Column(String)

    user_id = Column('users_id', Integer, ForeignKey('glpi_users.id'))
    user = relationship('User')

    ticket_id = Column('items_id', Integer, ForeignKey('glpi_tickets.id')) 
    ticket = relationship('Ticket')    

    @hybrid_property
    def start_date(self):
        starttime_div = get_div(self.content, 'starttime')
        all_p = starttime_div.find_all('p')

        for p in all_p:
            format_str = '%d/%m/%y %H:%M'
            try:
                datetime_str = p.get_text()
                parsed_datetime = datetime.datetime.strptime(datetime_str, format_str)
                return parsed_datetime.strftime("%Y-%m-%d")
            except ValueError as e:
                pass
        
        return None


    @hybrid_property
    def end_date(self):
        endtime_div = get_div(self.content, 'endtime')   
        all_p = endtime_div.find_all('p')
        for p in all_p:
            format_str = '%d/%m/%y %H:%M'
            try:
                datetime_str = p.get_text()
                parsed_datetime = datetime.datetime.strptime(datetime_str, format_str)
                return parsed_datetime.strftime("%Y-%m-%d")
            except ValueError as e:
                pass
        return None


    @hybrid_property
    def update(self):
        get_p_content(self.content, 'updates')


class TicketTask(Base):
    __tablename__ = 'glpi_tickettasks'

    id = Column(Integer, primary_key=True)
    user_id = Column('users_id', Integer, ForeignKey('glpi_users.id'))
    uuid = Column(CHAR(36), default=lambda: str(uuid4()))
    date = Column(DateTime)
    content = Column(String)
    duration = Column('actiontime', Integer)
    user_id = Column('users_id', Integer, ForeignKey('glpi_users.id'))
    users_id_editor = Column(Integer, default=0)
    ticket_id = Column('tickets_id', Integer, ForeignKey('glpi_tickets.id')) 
    technician_id = Column('users_id_tech', Integer)
    group_id = Column('groups_id_tech', Integer)
    state = Column(Integer, default=1)
    is_private = Column(Integer, default=0)
    date_mod = Column(DateTime)
    date_creation = Column(DateTime)
    taskcategories_id = Column(Integer, default=0)
    timeline_position = Column(Integer, default=1)
    sourceitems_id = Column(Integer, default=0)
    sourceof_items_id = Column(Integer, default=0)

    ticket_id = Column('tickets_id', Integer, ForeignKey('glpi_tickets.id'))
    ticket = relationship('Ticket', back_populates='ticket_tasks') 
    
    @hybrid_property
    def start_date(self):
        return self.date.strftime("%Y-%m-%d %H:%M")
    
    @hybrid_property
    def end_date(self):
        return self.date + datetime.timedelta(0, self.duration)

    @hybrid_property
    def duration_hr(self):
        return self.duration / 3600.0

    @hybrid_property
    def short_description(self):
        return get_p_content(self.content, 'task')

    @hybrid_property
    def additional_details(self):
        return get_p_content(self.content, 'details')
    
    @hybrid_property
    def ticket_name(self):
        return self.ticket.name

# Log model for glpi_logs
class Log(Base):
    __tablename__ = 'glpi_logs'

    id = Column(Integer, primary_key=True)
    itemtype = Column(String)
    items_id = Column(Integer)
    itemtype_link = Column(String)
    linked_action = Column(Integer)
    user_name = Column(String)
    date_mod = Column(DateTime)
    id_search_option = Column(Integer)
    old_value = Column(String)
    new_value = Column(String)

# Event model for glpi_events
class Event(Base):
    __tablename__ = 'glpi_events'

    id = Column(Integer, primary_key=True)
    items_id = Column(Integer)
    type = Column(String)
    date = Column(DateTime)
    service = Column(String)
    level = Column(Integer)
    message = Column(String)


