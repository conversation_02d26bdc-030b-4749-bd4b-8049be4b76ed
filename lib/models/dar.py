from sqlalchemy import Column, Integer, String, Float, BigInteger, DateTime, ForeignKey, Text
from sqlalchemy.ext.hybrid import hybrid_property, hybrid_method
from database import Base
from sqlalchemy.orm import relationship
from bs4 import BeautifulSoup
from datetime import datetime
import html

class DarEntry(Base):
    __tablename__ = 'dar_entry'

    id = Column(Integer, primary_key=True)
    
    user_id = Column(Integer)
    week = Column(Integer)
    start_dt = Column(DateTime)
    start_date = Column(String(255))
    start_time = Column(String(255))
    end_time = Column(String(255))
    task = Column(Text)
    source_type = Column(String(255))
    source_id = Column(Integer)
    parent_ticket_id = Column(Integer)
    parent_ticket_title = Column(String(255))


class DarSetting(Base):
    __tablename__ = 'dar_setting'

    id = Column(Integer, primary_key=True)

    name = Column(String(255))
    subname = Column(String(255))
    value = Column(String(255))
