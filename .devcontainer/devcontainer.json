// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/docker-existing-dockerfile
{
	"name": "Existing Dockerfile",
	"build": {
		// Sets the run context to one level up instead of the .devcontainer folder.
		"context": "..",
		// Update the 'dockerFile' property if you aren't using the standard 'Dockerfile' filename.
		"dockerfile": "../Dockerfile",
	},


	"containerEnv": { 
		"GLPIAPI_DB_USER": "root",
		"GLPIAPI_DB_HOST": "************",
		"GLPIAPI_DB_PORT": "3306",
		"GLPIAPI_DB_PWD": "M@sunur1nITSM",
		"GLPIAPI_DB_NAME": "glpidb",
		"GLPIAPI_APPDB_USER": "root",
		"GLPIAPI_APPDB_HOST": "************",
		"GLPIAPI_APPDB_PORT": "3306",
		"GLPIAPI_APPDB_PWD": "M@sunur1nITSM",
		"GLPIAPI_APPDB_NAME": "appdb"
	},
	// Features to add to the dev container. More info: https://containers.dev/features.
	"features": {
	},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	"forwardPorts": [9999],

	// Uncomment the next line to run commands after the container is created.
	"postCreateCommand": "uvicorn main:app --port 9999 --host=0.0.0.0 --log-level debug"

	// Configure tool-specific properties.
	// "customizations": {},

	// Uncomment to connect as an existing user other than the container default. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "devcontainer"
}
