# GLPI API

A FastAPI-based API for GLPI integration and DAR (Daily Activity Report) management.

## Features

- GLPI integration for tickets, entities, groups, and more
- DAR (Daily Activity Report) management
- Automatic database migrations during application startup
- RESTful API with OpenAPI documentation

## Installation

### Using Docker

```bash
# Build and run using Docker Compose
docker-compose up -d
```

### Manual Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python main.py
```

## Database Migrations

The application is configured to automatically run database migrations during startup. This means that when you deploy the application to production, the database schema will be automatically updated to the latest version without requiring manual intervention.

For more information about database migrations, see [MIGRATIONS.md](MIGRATIONS.md).

## API Documentation

Once the application is running, you can access the API documentation at:

- Swagger UI: `/docs`
- ReDoc: `/redoc`

## Environment Variables

The application uses environment variables for configuration. Create a `.env` file in the root directory with the following variables:

```
GLPIAPI_DB_USER=your_db_user
GLPIAPI_DB_PASSWORD=your_db_password
GLPIAPI_DB_HOST=your_db_host
GLPIAPI_DB_PORT=your_db_port
GLPIAPI_DB_NAME=your_db_name

GLPIAPI_DB2_USER=your_app_db_user
GLPIAPI_DB2_PASSWORD=your_app_db_password
GLPIAPI_DB2_HOST=your_app_db_host
GLPIAPI_DB2_PORT=your_app_db_port
GLPIAPI_DB2_NAME=your_app_db_name
```

## License

This project is proprietary and confidential.