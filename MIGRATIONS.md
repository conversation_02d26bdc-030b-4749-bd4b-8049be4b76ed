# Database Migrations Guide

## Automatic Migrations

The application is configured to automatically run database migrations during startup. This means that when you deploy the application to production, the database schema will be automatically updated to the latest version without requiring manual intervention.

The migration process uses Alembic to manage database schema changes. When the application starts, it will:

1. Load environment variables
2. Run all pending Alembic migrations
3. Continue with application startup

## Manual Migrations

If you need to run migrations manually (for example, during development or to troubleshoot migration issues), you can use the provided script:

```bash
python run_migrations.py
```

This script will run all pending migrations without starting the full application.

## Creating New Migrations

When you make changes to the database schema, you should create a new migration:

```bash
alembic revision --autogenerate -m "description of changes"
```

This will create a new migration file in the `alembic/versions` directory. Review the generated migration file to ensure it correctly captures your intended changes.

## Migration Configuration

The migration configuration is stored in `alembic.ini` and `alembic/env.py`. The database connection string is read from the application's environment variables, so you don't need to modify these files when deploying to different environments.

## Troubleshooting

If you encounter issues with migrations:

1. Check the application logs for error messages
2. Verify that the database connection string is correct
3. Try running migrations manually using the `run_migrations.py` script
4. If necessary, you can run specific migration commands using Alembic directly:

```bash
# Show current migration version
alembic current

# Show migration history
alembic history

# Run specific migration
alembic upgrade <revision>
```